命令: show configuration snapshot
设备: ************
时间: 2025-07-15 21:29:53.258775
==================================================
! Chassis:
system name "OS2260"
system location "ALE-GZ-SmartCity-V9"

! Configuration:
configuration error-file-limit 2

! Capability Manager:
! Virtual Flow Control:
! Interface:
interfaces port 1/1/6 alias " link to Dell 5490 laptop"
interfaces port 1/1/8 alias " link to FW-inside port"
interfaces ddm enable
interfaces ddm-trap enable

! Port_Manager: 
! Link Aggregate:
! VLAN:
vlan 1 admin-state enable
vlan 30 admin-state enable

! Spanning Tree:
spantree vlan 1 admin-state enable 
spantree vlan 30 admin-state enable 

! DA-UNP:
! Bridging:
! Port Mirroring:
! Port Mapping:
! IP:
ip interface "vlan1" address ************ mask ************* vlan 1 ifindex 2
ip interface "vlan 10 for office" address ************ mask ************* vlan 10 ifindex 3
ip interface "vlan 20 for guest" address ************ mask ************* vlan 20 ifindex 4
ip interface "vlan30" address *********** mask ************* vlan 30 ifindex 5
ip service source-ip "vlan1" swlog

! IPv6:
! IPMS:
! AAA:
aaa authentication default "local" 
aaa authentication console "local" 
aaa authentication http "local" 
aaa authentication snmp "local" 

user password-size min 6
user password-policy min-uppercase 0
user password-policy min-lowercase 0
user password-policy min-digit 0
user password-policy min-nonalpha 0
user password-history 2

! NTP:
ntp server clock0.ovcirrus.com
ntp server clock3.ovcirrus.com
ntp server clock1.ovcirrus.com
ntp server clock2.ovcirrus.com
ntp server ntp1.baidu.com
ntp server ntp.org
ntp server ************
ntp server *************
ntp client admin-state enable

! QOS:
qos disable

! Policy Manager:
! ERP:
! MVRP:
! LLDP:
lldp nearest-bridge chassis tlv management port-description enable system-name enable system-description enable

! UDLD:
! Session Manager:
session cli timeout 99999
session prompt default "OS2260-P10->"

! Web:
! Trap Manager:
! Health Monitor:
health threshold memory 5

! System Service:
swlog output socket *********** 10514
ip name-server ***************
system timezone ZP8

! SNMP:
snmp security no-security
snmp community-map mode enable
snmp community-map hash-key c47fdc198d69417e user "snmpuser" enable

! IP Route Manager:
ip static-route 0.0.0.0/0 gateway ************ metric 1 

! VRRP:
! RIP:
! OSPF:
! RIPng:
! OSPF3:
! LAN Power:
lanpower slot 1/1 class-detection enable

! DHCPv6 Relay:
! DHCPv6 Snooping:
! Virtual Chassis Split Protection:
! DHCP Snooping:
! DHCP Server:
! Loopback Detection:
! OVC:
! IP DHCP RELAY:
! LOOPBACK TEST:
! MGMT AGENT:
