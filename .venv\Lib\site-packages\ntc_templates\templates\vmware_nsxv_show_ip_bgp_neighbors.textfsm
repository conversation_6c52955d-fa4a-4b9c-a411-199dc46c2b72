Value NEIGHBOR_ID (\d+(\.\d+){3})
Value REMOTE_AS (\d+)
Value STATE (\D.*)
Value HOLD_INTERVAL (\d+)
Value KEEPALIVE_INTERVAL (\d+)
Value PFX_RECV (\d+)
Value PFX_SENT (\d+)
Value PFX_ADV (\d+)

Start
	^BGP neighbor.* -> Continue.Record
	^BGP neighbor is ${NEIGHBOR_ID},\s+remote AS ${REMOTE_AS}
	^BGP state = ${STATE}
	^Hold time is ${HOLD_INTERVAL}, Keep alive interval is ${KEEPALIVE_INTERVAL}
	^\s+Prefixes received ${PFX_RECV} sent ${PFX_SENT} advertised ${PFX_ADV}