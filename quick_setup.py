#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
快速设置脚本
在虚拟环境中快速配置网络设备巡检系统
"""

import os
import sys
import subprocess

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("网络设备巡检系统 - 快速设置")
    print("Network Device Inspection System - Quick Setup")
    print("=" * 60)

def check_virtual_env():
    """检查是否在虚拟环境中"""
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    )
    
    if in_venv:
        print("✓ 检测到虚拟环境")
        print(f"  Python路径: {sys.executable}")
        return True
    else:
        print("! 未检测到虚拟环境")
        print("  建议在虚拟环境中运行此程序")
        choice = input("是否继续? (y/n): ").lower().strip()
        return choice in ['y', 'yes', '是']

def install_dependencies():
    """安装依赖包"""
    print("\n步骤 1: 安装依赖包")
    print("-" * 30)
    
    dependencies = [
        "netmiko>=4.0.0",
        "openpyxl>=3.0.0", 
        "prettytable>=3.0.0",
        "pandas>=1.3.0",
        "paramiko>=2.7.0"
    ]
    
    try:
        for dep in dependencies:
            print(f"安装 {dep}...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", dep],
                capture_output=True,
                text=True,
                check=True
            )
            print(f"  ✓ {dep} 安装成功")
        
        print("✓ 所有依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 安装失败: {e}")
        print("请手动运行: pip install -r requirements.txt")
        return False

def create_template():
    """创建Excel模板"""
    print("\n步骤 2: 创建Excel模板")
    print("-" * 30)
    
    try:
        # 运行模板创建脚本
        result = subprocess.run(
            [sys.executable, "create_simple_template.py"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✓ Excel模板创建成功")
            print(result.stdout)
            return True
        else:
            print("✗ Excel模板创建失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 创建模板时出错: {e}")
        return False

def test_modules():
    """测试模块导入"""
    print("\n步骤 3: 测试模块")
    print("-" * 30)
    
    modules_to_test = [
        ("netmiko", "网络设备连接"),
        ("openpyxl", "Excel文件处理"),
        ("prettytable", "表格格式化"),
        ("pandas", "数据处理"),
        ("paramiko", "SSH客户端")
    ]
    
    all_ok = True
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"  ✓ {module_name} - {description}")
        except ImportError:
            print(f"  ✗ {module_name} - {description} (未安装)")
            all_ok = False
    
    # 测试自定义模块
    custom_modules = [
        ("connect", "设备连接模块"),
        ("send_email", "邮件发送模块"),
        ("zip_file", "文件压缩模块")
    ]
    
    for module_name, description in custom_modules:
        try:
            __import__(module_name)
            print(f"  ✓ {module_name}.py - {description}")
        except ImportError:
            print(f"  ! {module_name}.py - {description} (文件可能不存在)")
    
    return all_ok

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "=" * 60)
    print("设置完成! 后续步骤:")
    print("=" * 60)
    print("1. 编辑 template.xlsx 文件:")
    print("   - 在'设备信息'工作表中配置您的设备")
    print("   - 设备IP、用户名、密码、设备类型等")
    print("   - 状态列填写'#'可跳过设备")
    print()
    print("2. 配置巡检命令:")
    print("   - 在对应设备类型的工作表中配置命令")
    print("   - 如: cisco_ios, huawei, alcatel_aos 等")
    print()
    print("3. 运行巡检程序:")
    print("   - 简化版: python simple_inspection.py")
    print("   - 完整版: python network_inspection.py")
    print("   - 自动模式: python simple_inspection.py --auto")
    print()
    print("4. 可选配置:")
    print("   - 编辑 send_email.py 配置邮件发送")
    print("   - 调整 connect.py 中的并发数设置")
    print()
    print("支持的设备类型:")
    print("  - cisco_ios (Cisco设备)")
    print("  - huawei (华为设备)")
    print("  - alcatel_aos (ALE设备)")
    print("  - ruijie_os (锐捷设备)")
    print("  - hp_comware (H3C设备)")
    print("=" * 60)

def main():
    """主函数"""
    print_banner()
    
    # 检查虚拟环境
    if not check_virtual_env():
        return
    
    # 安装依赖
    if not install_dependencies():
        print("\n依赖安装失败，请手动安装后重试")
        return
    
    # 创建模板
    if not create_template():
        print("\n模板创建失败，请手动运行 create_simple_template.py")
    
    # 测试模块
    if test_modules():
        print("\n✓ 所有模块测试通过")
    else:
        print("\n! 部分模块测试失败，但可以继续使用")
    
    # 显示后续步骤
    show_next_steps()

if __name__ == '__main__':
    main()
