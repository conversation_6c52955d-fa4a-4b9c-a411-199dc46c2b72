#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
测试修改后的功能
"""

import os
from ale_inspection import ALEInspection

def test_vendor_mapping():
    """测试厂商名称映射"""
    print("=" * 50)
    print("测试厂商名称映射")
    print("=" * 50)
    
    inspector = ALEInspection()
    
    test_types = [
        'cisco_ios', 'cisco_xe', 'huawei', 'alcatel_aos', 
        'h3c_comware', 'ruijie_os', 'juniper_junos', 'unknown_type'
    ]
    
    for device_type in test_types:
        vendor = inspector.get_vendor_name(device_type)
        print(f"{device_type:15} -> {vendor}")

def test_zip_path():
    """测试压缩包路径"""
    print("\n" + "=" * 50)
    print("测试压缩包路径")
    print("=" * 50)
    
    log_dir = 'LOG'
    timestamp = '20250716_123456'
    zip_path = os.path.join(log_dir, f'inspection_results_{timestamp}.zip')
    print(f"压缩包路径: {zip_path}")
    print(f"是否在LOG目录内: {zip_path.startswith('LOG')}")

def test_device_classification():
    """测试设备分类逻辑"""
    print("\n" + "=" * 50)
    print("测试设备分类逻辑")
    print("=" * 50)
    
    inspector = ALEInspection()
    
    test_devices = [
        {'ip': '***********', 'device_type': 'alcatel_aos'},
        {'ip': '***********', 'device_type': 'cisco_ios'},
        {'ip': '***********', 'device_type': 'huawei'},
        {'ip': '***********', 'device_type': 'h3c_comware'},
    ]
    
    ale_devices = []
    other_devices = []
    vendor_count = {}
    
    for device in test_devices:
        device_type = device['device_type'].lower()
        if 'alcatel' in device_type or 'ale' in device_type:
            ale_devices.append(device)
        else:
            other_devices.append(device)
            vendor = inspector.get_vendor_name(device_type)
            vendor_count[vendor] = vendor_count.get(vendor, 0) + 1
    
    print(f"ALE设备: {len(ale_devices)} 个")
    for vendor, count in vendor_count.items():
        print(f"{vendor}设备: {count} 个")

if __name__ == '__main__':
    test_vendor_mapping()
    test_zip_path()
    test_device_classification()
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
