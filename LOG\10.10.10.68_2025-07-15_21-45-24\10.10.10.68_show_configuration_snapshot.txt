命令: show configuration snapshot
设备: ***********
时间: 2025-07-15 21:45:40.902432
==================================================
! Chassis:
system name "OS6860E-24"
system location "GZ-Smart City"

! Configuration:
configuration error-file-limit 2

! Capability Manager:
! Virtual Flow Control:
! LFP: 
! Interface:
interfaces port 1/1/5 EEE enable
interfaces port 1/1/6 EEE enable
interfaces port 1/1/7 EEE enable
interfaces port 1/1/8 EEE enable
interfaces port 1/1/9 EEE enable
interfaces port 1/1/10 EEE enable
interfaces port 1/1/11 EEE enable
interfaces port 1/1/12 EEE enable
interfaces port 1/1/13 EEE enable
interfaces port 1/1/14 EEE enable
interfaces port 1/1/15 EEE enable
interfaces port 1/1/16 EEE enable
interfaces port 1/1/17 EEE enable
interfaces port 1/1/18 EEE enable
interfaces port 1/1/19 EEE enable
interfaces port 1/1/20 EEE enable
interfaces port 1/1/21 EEE enable
interfaces port 1/1/22 EEE enable
interfaces port 1/1/23 EEE enable
interfaces port 1/1/24 EEE enable
interfaces ddm enable
interfaces ddm-trap enable

! Port_Manager: 
! Link Aggregate:
linkagg static agg 1 size 2 admin-state enable 
linkagg lacp agg 2 size 8 admin-state enable 
linkagg lacp agg 2 actor admin-key 1
linkagg lacp port 1/1/21 actor admin-key 1
linkagg lacp port 1/1/22 actor admin-key 1

! VLAN:
vlan 1 admin-state enable
vlan 493 admin-state enable
vlan 593 admin-state enable

! PVLAN:
! Spanning Tree:
spantree vlan 1 admin-state enable 
spantree vlan 493 admin-state enable 
spantree vlan 593 admin-state enable 

! DA-UNP:
unp profile "devProfPrinter" 
unp profile "devProfWindows" 
unp profile "devProfIP-Phone" 
unp profile "devProfWireless-Router" 
unp profile "devProfSmartPhone/PDA/Tablets" 
unp classification-rule "devProfPrinter"
unp classification-rule "devProfPrinter" precedence 255
unp classification-rule "devProfPrinter" Profile1 "devProfPrinter"
unp classification-rule "devProfPrinter" device-type "Printer"
unp classification-rule "devProfWindows"
unp classification-rule "devProfWindows" precedence 255
unp classification-rule "devProfWindows" Profile1 "devProfWindows"
unp classification-rule "devProfWindows" device-type "Windows"
unp classification-rule "devProfIP-Phone"
unp classification-rule "devProfIP-Phone" precedence 255
unp classification-rule "devProfIP-Phone" Profile1 "devProfIP-Phone"
unp classification-rule "devProfIP-Phone" device-type "IP-Phone"
unp classification-rule "devProfWireless-Router"
unp classification-rule "devProfWireless-Router" precedence 255
unp classification-rule "devProfWireless-Router" Profile1 "devProfWireless-Router"
unp classification-rule "devProfWireless-Router" device-type "Wireless-Router"
unp classification-rule "devProfSmartPhone/PDA/Tablets"
unp classification-rule "devProfSmartPhone/PDA/Tablets" precedence 255
unp classification-rule "devProfSmartPhone/PDA/Tablets" Profile1 "devProfSmartPhone/PDA/Tablets"
unp classification-rule "devProfSmartPhone/PDA/Tablets" device-type "SmartPhone/PDA/Tablets"

! Bridging:
! Port Mirroring:
! Port Mapping:
! IP:
ip interface "Loopback0" address ******* 
ip interface "vlan10" address ************** mask ************* vlan 10 ifindex 1
ip interface "vlan1" address *********** mask ************* vlan 1 ifindex 2
ip interface "no" ifindex 4
ip interface "vlan30" address ************* mask ************* vlan 30 ifindex 5
ip interface "vlan40" address ************* mask ************* vlan 40 ifindex 7
ip service source-ip "vlan1" swlog

! IPv6:
! IPSec:
! IPMS:
! AAA:
aaa authentication default "local" 
aaa authentication console "local" 
aaa authentication telnet "local" 
aaa authentication http "local" 
aaa authentication snmp "local" 
aaa tacacs command-authorization disable

! NTP:
ntp authenticate enable
ntp server *************
ntp server *************
ntp broadcast-client enable
ntp client admin-state enable

! TCAM Manager:
! QOS:
qos disable
qos apply

! Policy Manager:
! VLAN Stacking:
! ERP:
! MVRP:
! LLDP:
lldp nearest-bridge port 1/1/1-28 tlv management system-description enable
lldp nearest-bridge port 1/1/1-28 tlv management system-name enable
lldp nearest-bridge port 1/1/1-28 tlv management management-address enable

! UDLD:
! Server Load Balance:
! High Availability Vlan:
! Session Manager:
session prompt default "zdnf-os6860->"

! Web:
webview force-ssl disable

! Trap Manager:
! Health Monitor:
health threshold memory 80

! System Service:
swlog output socket *********** 10514
swlog output socket console enable
system timezone ZP8

! SNMP:
snmp security no-security
snmp community-map mode enable
snmp community-map hash-key c47fdc198d69417e user "snmpuser" enable

! BFD:
! IP Route Manager:
ip router router-id *******
ip static-route 0.0.0.0/0 gateway ************ metric 1 

! VRRP:
ip vrrp 30 interface "vlan30" priority 100 preempt interval 100 version v2
ip vrrp 30 interface "vlan30" address **************
ip vrrp 30 interface "vlan30" admin-state enable

! UDP Relay:
! RIP:
! OSPF:
ip load ospf
ip ospf area 0.0.0.0
ip ospf area *******
ip ospf area *******
ip ospf interface "vlan1"
ip ospf interface "vlan1" area 0.0.0.0
ip ospf interface "vlan1" admin-state enable 
ip ospf interface "vlan30"
ip ospf interface "vlan30" area *******
ip ospf interface "vlan30" admin-state enable 
ip ospf interface "vlan10"
ip ospf interface "vlan10" area *******
ip ospf interface "vlan10" admin-state enable 
ip ospf admin-state enable

! IP Multicast:
! DVMRP:
! IPMR:
! RIPng:
! OSPF3:
! BGP:
! ISIS:
! Module:
! LAN Power:
! RDP:
! DHL:
! Ethernet-OAM:
! SAA:
! SPB-ISIS:
! SVCMGR:
! EVB:
! APP-FINGERPRINT:
! FCOE:
! QMR: 
! OPENFLOW:
! Dynamic auto-fabric:
! SIP Snooping:
! DHCP Server:
! DHCPv6 Relay:
! DHCPv6 Snooping:
! DHCPv6 Server:
! DHCP Message Service:
! DHCP Active Lease Service:
! Virtual Chassis Split Protection:
! DHCP Snooping:
! APP-MONITORING:
app-mon separate-config-file

! Loopback Detection:
loopback-detection enable

! VM-SNOOPING:
! PPPOE-IA:
! Security:
! Zero Configuration:
! MAC Security:
! OVC:
! EFM-OAM:
! ALARM-MANAGER:
! DEVICE-PROFILE:

device-profile admin-state enable

! PTP:
! IP DHCP RELAY:
ip dhcp relay admin-state enable

! TEST-OAM:
! LOOPBACK TEST:
! UDP6 RELAY:
! MGMT AGENT:
! MRP:
! PKGMGR:
pkgmgr install package-webview-8.6.R02-203.deb

! SITEMGR:
! SWLIC:
! MPLS:
! LDP:
! PROFINET:
