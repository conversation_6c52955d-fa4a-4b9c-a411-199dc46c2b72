
=======
LAYER 3
=======

#########################################
show ip routes
#########################################

 + = Equal cost multipath routes
 Total 3 routes

  Dest Address       Gateway Addr        Age        Protocol 
------------------+-------------------+----------+-----------
  0.0.0.0/0            **********          27d 8h   STATIC    
  **********/24        ************        27d 8h   LOCAL     
  127.0.0.1/32         127.0.0.1           27d 8h   LOCAL     


#########################################
show ip router database
#########################################
Legend: + indicates routes in-use
        b indicates BFD-enabled static route
        i indicates interface static route
        r indicates recursive static route, with following address in brackets

Total IPRM IPv4 routes: 3

  Destination         Gateway                   Interface              Protocol  Metric     Tag      Misc-Info
---------------------+---------------+--------------------------------+--------+-------+----------+-----------------
+  0.0.0.0/0          **********      management                       STATIC         1          0  
+  **********/24      ************    management                       LOCAL          1          0  
+  127.0.0.1/32       127.0.0.1       Loopback                         LOCAL          1          0  


Inactive Static Routes
   Destination       Gateway           Metric        Tag   Misc-Info
--------------------+-----------------+------+----------+-----------------


#########################################
show ip access-list
#########################################
Access Lists: ipv4 configured: 0 ipv6 configured: 0 total configured: 0 max: 0

Address /    Redistribution
Name   Prefix Length      Effect  Control
--------------------+------------------+-------+--------------


#########################################
show ip route-map
#########################################
Route Maps: configured: 0 max: 200


#########################################
show ip redist
#########################################

Source       Destination
Protocol     Protocol     Admin State  Route Map
------------+------------+-----------+--------------------


#########################################
show ip traffic
#########################################

Summary:
Datagrams received
  Total                   =     349554,
  IP header error         =          0,
  Destination IP error    =          0,
  Unknown protocol        =          0,
  Local discards          =          0,
  Delivered to users      =     349554,
  Reassemble needed       =          0,
  Reassembled             =          0,
  Reassemble failed       =          0

Datagrams sent
  Forwarded               =          0,
  Generated               =     349554,
  Local discards          =          0,
  No route discards       =          0,
  Fragmented              =          0,
  Fragment failed         =          0,
  Fragments generated     =          0

Chassis 1 Slot 1:
Datagrams received
  Total                   =     349554,
  IP header error         =          0,
  Destination IP error    =          0,
  Unknown protocol        =          0,
  Local discards          =          0,
  Delivered to users      =     349554,
  Reassemble needed       =          0,
  Reassembled             =          0,
  Reassemble failed       =          0


Datagrams sent
  Forwarded               =          0,
  Generated               =     349554,
  Local discards          =          0,
  No route discards       =          0,
  Fragmented              =          0,
  Fragment failed         =          0,
  Fragments generated     =          0



#########################################
show icmp statistics
#########################################

  Messages                    Received         Sent
---------------------------+----------+-------------
  Total                         354007      1548575
  Error                           4047            0
  Destination unreachable         4451      1199019
  Time exceeded                      0            0
  Parameter problem                  0            0
  Source quench                      0            0
  Redirect                           0            0
  Echo request                  349554            2
  Echo reply                         2       349554
  Timestamp Request                  0            0
  Timestamp Reply                    0            0
  Address mask request               0            0
  Address mask reply                 0            0


#########################################
show tcp statistics
#########################################

Total segments received =     78327902,
Error segments received =            0,
Total segments sent     =     78754155,
Segments retransmitted  =       836245,
Reset segments sent     =      5471426,
Connections initiated   =      6004007,
Connections accepted    =       470956,
Connections established =            1,
Attempt fails           =      5468431,
Established resets      =         9014



#########################################
show tcp ports
#########################################

  Local Address     Local Port   Remote Address    Remote Port    State       
------------------+------------+-----------------+--------------+-------------
  0.0.0.0                   22   0.0.0.0                     0    LISTEN        
  0.0.0.0                  111   0.0.0.0                     0    LISTEN        
  0.0.0.0                  443   0.0.0.0                     0    LISTEN        
  0.0.0.0                10161   0.0.0.0                     0    LISTEN        
  0.0.0.0                32053   0.0.0.0                     0    LISTEN        
  ************              23   ************            26089    ESTABLISHED   



#########################################
show udp statistics
#########################################

Total datagrams received   =       174806,
Error datagrams received   =            0,
No port datagrams received =          240,
Total datagrams sent       =       877090



#########################################
show udp ports
#########################################

  Local Address     Local Port   
------------------+--------------
  0.0.0.0                   67
  0.0.0.0                  111
  0.0.0.0                  123
  0.0.0.0                  161
  0.0.0.0                  843
  0.0.0.0                  946
  0.0.0.0                 1812
  0.0.0.0                 3799
  0.0.0.0                39379
  ************             123
  0.0.0.0                  161
  0.0.0.0                35302



#########################################
show ip vrrp
#########################################
ERROR: specified application not loaded


#########################################
show ip vrrp statistics
#########################################
ERROR: specified application not loaded


#########################################
show arp
#########################################

Total 3 arp entries
 Flags (P=Proxy, A=Authentication, V=VRRP, B=BFD, H=HAVLAN, I=INTF, M=Managed)

 IP Addr           Hardware Addr       Type       Flags   Port              Interface   Name
-----------------+-------------------+----------+-------+-----------------+-----------+---------------------------------
 ***********       00:50:56:ad:bd:91   DYNAMIC                       1/1/9  management                                  
 ************      30:43:d7:ef:59:79   DYNAMIC                       1/1/9  management                                  
 ************      3c:97:0e:61:1f:fa   DYNAMIC                       1/1/4  management                                  



#########################################
show qos log
#########################################
**QOS Log**
 8/02/23  2:53:54 Log Init (64c9c542).
 8/02/23  2:53:54 Connect from API 1/0
 8/02/23  2:53:54 Connect socket TCAMCmm
 8/02/23  2:53:54 qosmHandleTcamMgrConnect: ret 0 sending Hello.
 8/02/23  2:53:55 QoS registered with Chassis Supervisor
 8/02/23  2:53:55 QoS registered with MIP library
 8/02/23  2:53:55 QoS registered with Vlan Manager
 8/02/23  2:53:55 QoS registered with ipCmm
 8/02/23  2:53:55 QoS registered with ipv6
 8/02/23  2:53:55 qosmHandleTcamMgrMsg: HELLO ACK msg received from TCAM Manager CMM
 8/02/23  2:53:55 qoscEpHandleConnectPm: Connected to Port Manager.
 8/02/23  2:53:55 Got eoic (64c9c543)
 8/02/23  2:53:55 Calling cslib_unblock (64c9c543)
 8/02/23  2:54:17 Connect from API 1/1
 8/02/23  2:54:19 add VRF [id 0] name "default"
 8/02/23  2:54:47 qosmHandleTcamMgrMsg: SLOT STATUS received from TCAM Manager CMM
 8/02/23  2:54:54 VM_PORT_STATE_CHANGE 1:1/12 inactive
 8/02/23 10:52:07 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/02/23 10:52:09 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/02/23 10:54:44 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/03/23  2:00:51 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/03/23 10:47:00 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23  1:56:35 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23  1:56:49 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23  7:31:40 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23  7:32:00 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23  8:59:49 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23  8:59:55 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23  9:00:10 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23 10:15:08 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23 10:15:22 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23 10:22:38 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23 10:22:52 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/06/23 10:28:55 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/07/23  2:10:10 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/07/23 11:14:43 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/08/23  3:29:36 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/08/23  4:21:56 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/08/23  4:22:17 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/08/23 11:37:40 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/08/23 11:37:48 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/09/23  2:09:42 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/09/23  3:43:51 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/09/23  3:44:13 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/09/23  7:17:47 VM_PORT_STATE_CHANGE 1:1/12 inactive
 8/09/23 11:05:02 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/10/23  2:13:00 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/10/23  4:41:15 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/10/23  5:25:16 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/10/23  5:25:36 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/10/23  7:57:55 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/10/23  7:58:15 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/10/23 10:18:10 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/13/23  2:18:19 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/13/23  2:18:33 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/13/23  6:03:34 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/13/23  6:03:49 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/13/23  7:31:01 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/13/23  7:31:13 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/15/23  6:56:47 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/15/23 10:44:22 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/15/23 10:44:25 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/15/23 10:44:34 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/16/23  2:05:43 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/16/23  2:05:58 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/16/23 11:48:26 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/16/23 11:48:51 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/17/23  2:18:18 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/17/23  3:47:25 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/17/23  3:47:40 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/17/23  6:40:01 VM_PORT_STATE_CHANGE 1:1/12 inactive
 8/17/23  6:40:22 VM_PORT_STATE_CHANGE 1:1/12 inactive
 8/17/23 10:25:00 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/17/23 10:25:02 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/17/23 10:25:08 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/20/23  2:15:57 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/20/23 14:34:16 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/21/23  1:58:10 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/21/23  1:58:24 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/21/23  9:07:52 VM_PORT_STATE_CHANGE 1:1/3 inactive
 8/21/23 10:40:08 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/22/23 11:07:11 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/23/23  4:39:39 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/23/23  4:39:53 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/23/23 10:57:18 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/24/23  2:10:56 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/24/23  2:11:10 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/24/23 10:26:30 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/24/23 10:26:32 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/24/23 10:26:38 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/27/23  3:42:28 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/27/23 10:20:44 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/28/23  2:14:44 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/28/23 10:51:39 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/29/23  1:49:58 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/29/23  2:44:22 VM_PORT_STATE_CHANGE 1:1/3 inactive
 8/29/23  2:47:09 VM_PORT_STATE_CHANGE 1:1/9 inactive
 8/29/23 10:24:12 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/29/23 10:24:14 VM_PORT_STATE_CHANGE 1:1/4 inactive
 8/29/23 10:24:29 VM_PORT_STATE_CHANGE 1:1/4 inactive


#########################################
show qos config
#########################################
QoS Configuration
  Admin                          = enable,
  Trust ports                    = no,
  Log lines                      = 10240,
  Log level                      = 6,
  Log console                    = no,
  Forward log                    = no,
  User-port filter               = spoof ,
  User-port shutdown             = none,
  Phones                         = trusted,
  DEI Mapping                    = disable,
  DEI Marking                    = disable


#########################################
show qos port
#########################################
Slot/                 Default    Default               Bandwidth           DEI
Port    Active  Trust P/DSCP Classification  Physical  Ingress Egress    Map Mark   Type
-------+-------+-----+------+--------------+----------+-------+------+------+------+-------------
1/1/1      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/2      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/3      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/4     Yes      No  0/ 0           DSCP         1G       -       -    No    No   ethernet-1G
1/1/5     Yes      No  0/ 0           DSCP         1G       -       -    No    No   ethernet-1G
1/1/6      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/7      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/8      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/9     Yes      No  0/ 0           DSCP        10G       -       -    No    No   ethernet-10G
1/1/10     No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/11     No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/12     No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet


#########################################
show tcam utilization detail
#########################################

Legend:
 C/S/T = Chassis/Slot/TCAM
 PI = Pre-Ingress
 I  = Ingress
 E  = Egress

             App                       App            Resource                             Entry
 C/S/T       Group                     Name             Name                       Stage   Size    Used   Reserved  Available
             Name                                                                                 Entries  Entries   Entries
------+------------------------+--------------------+-----------------------------+-----+--------+-------+---------+-------------------
 1/1/1 SYSHI                    -                     System High                    I      60       60      128       68
 1/1/1 SYSLO                    -                     System Low                     I      30       63      128       65
 1/1/1 CPUQ                     -                     System CPU-Q                   I      30       28      128      100
 1/1/1 -                        AG                    AG-Filter                      I      30        0      256      256
 1/1/1 -                        QOS                   QoS Policy Ingress             I      60        0      256      256
 1/1/1 -                        QOS                   QoS UNP-LIST                   I       0        0        0        0


#########################################
show qos statistics
#########################################
QoS stats
  Spoofed Events       : 0
  NonSpoofed Events    : 0


#########################################
show policy server long
#########################################
No servers


#########################################
show policy server statistics
#########################################
No policy server stats


#########################################
show policy server rules
#########################################
ERROR: display error (incorrect index = 0)


#########################################
show policy server events
#########################################
 Event time                        event description
-----------------+------------------------------------------------------
08/02/23 02:53:54 Initialized policy event log
08/02/23 02:53:55 Initialized LDAP


#########################################
show active policy rule
#########################################
No active rules

#########################################
show active policy list
#########################################
No applied lists
