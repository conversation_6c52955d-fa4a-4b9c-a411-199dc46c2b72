@echo off
chcp 65001 >nul
title ALE网络运维工具包 - GUI启动器

echo.
echo ========================================
echo    ALE网络运维工具包 - GUI版本
echo ========================================
echo.

echo 正在启动GUI界面...
python ale_gui.py

if %errorlevel% neq 0 (
    echo.
    echo 启动失败！可能的原因：
    echo 1. Python未安装或不在PATH中
    echo 2. 缺少必要的依赖库
    echo 3. 文件损坏或缺失
    echo.
    echo 解决方案：
    echo 1. 运行: python install_gui_requirements.py
    echo 2. 或者使用打包好的exe文件
    echo.
    pause
)
