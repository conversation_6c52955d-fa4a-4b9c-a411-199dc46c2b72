#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
创建完整的部署包
包含exe文件、配置文件和使用说明
"""

import os
import shutil
import subprocess
import sys
from datetime import datetime

def create_deployment_package():
    """创建完整的部署包"""
    print("=" * 60)
    print("ALE网络运维工具包 - 部署包创建工具")
    print("=" * 60)
    
    # 1. 检查必要文件
    print("\n1. 检查必要文件...")
    required_files = [
        'ale_gui.py',
        'ale_inspection.py', 
        'connect.py',
        'send_email.py',
        'env_loader.py',
        'zip_file.py',
        'tftp_downloader.py',
        'template.xlsx',
        '.env'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"✗ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✓ 所有必要文件检查通过")
    
    # 2. 安装依赖
    print("\n2. 安装打包依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller>=5.0.0"])
        print("✓ PyInstaller 安装成功")
    except subprocess.CalledProcessError:
        print("✗ PyInstaller 安装失败")
        return False
    
    # 3. 创建打包配置
    print("\n3. 创建打包配置...")
    create_complete_spec()
    
    # 4. 执行打包
    print("\n4. 开始打包...")
    try:
        subprocess.check_call(["pyinstaller", "ale_complete.spec", "--clean"])
        print("✓ 打包成功")
    except subprocess.CalledProcessError:
        print("✗ 打包失败")
        return False
    
    # 5. 创建部署目录
    print("\n5. 创建部署包...")
    deployment_dir = f"ALE网络运维工具包_部署包_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if os.path.exists(deployment_dir):
        shutil.rmtree(deployment_dir)
    
    os.makedirs(deployment_dir)
    
    # 6. 复制文件到部署目录
    print("6. 复制文件...")
    
    # 复制exe文件
    exe_source = "dist/ALE网络运维工具包.exe"
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, deployment_dir)
        print("✓ 复制exe文件")
    else:
        print("✗ 找不到exe文件")
        return False
    
    # 复制配置文件模板
    config_files = [
        ('template.xlsx', 'template.xlsx'),
        ('.env', '.env'),
        ('README.md', 'README.md'),
        ('README_CN.md', 'README_CN.md'),
        ('GUI使用说明.md', 'GUI使用说明.md')
    ]
    
    for src, dst in config_files:
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(deployment_dir, dst))
            print(f"✓ 复制 {src}")
    
    # 7. 创建使用说明
    print("7. 创建使用说明...")
    create_deployment_readme(deployment_dir)
    
    # 8. 创建启动脚本
    print("8. 创建启动脚本...")
    create_startup_scripts(deployment_dir)
    
    print(f"\n✓ 部署包创建完成: {deployment_dir}")
    print("\n部署包内容:")
    for root, dirs, files in os.walk(deployment_dir):
        level = root.replace(deployment_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
    
    return True

def create_complete_spec():
    """创建完整的打包配置"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ale_gui.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        # 网络和SSH库
        'netmiko',
        'netmiko.cisco',
        'netmiko.cisco.cisco_ios',
        'netmiko.huawei', 
        'netmiko.huawei.huawei',
        'netmiko.alcatel',
        'netmiko.alcatel.alcatel_aos',
        'netmiko.hp',
        'netmiko.hp.hp_comware',
        'netmiko.ruijie',
        'paramiko',
        'paramiko.client',
        'paramiko.ssh_exception',
        'paramiko.transport',
        'paramiko.channel',
        
        # Excel处理
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'openpyxl.reader',
        'openpyxl.writer',
        
        # 数据处理
        'pandas',
        'pandas.core',
        'pandas.io',
        'pandas.io.excel',
        
        # 邮件和网络
        'email',
        'email.mime',
        'email.mime.text',
        'email.mime.multipart',
        'email.mime.application',
        'email.mime.base',
        'smtplib',
        'ftplib',
        
        # 文件处理
        'zipfile',
        'os',
        'sys',
        'datetime',
        'time',
        'shutil',
        'glob',
        
        # GUI库
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tkinter.constants',
        
        # 多线程和进程
        'threading',
        'queue',
        'multiprocessing',
        'multiprocessing.pool',
        
        # 网络和系统
        'socket',
        'struct',
        're',
        'json',
        'base64',
        'hashlib',
        'ssl',
        'urllib',
        'urllib.parse',
        'subprocess',
        
        # 其他必要模块
        'logging',
        'traceback',
        'collections',
        'itertools',
        'functools'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
        'pytest',
        'sphinx'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ALE网络运维工具包',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('ale_complete.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 打包配置文件创建完成")

def create_deployment_readme(deployment_dir):
    """创建部署说明文件"""
    readme_content = '''# ALE网络运维工具包 - 部署包

## 🚀 快速开始

### 1. 配置文件
在运行程序前，请先配置以下文件：

#### Excel配置文件 (template.xlsx)
- 配置设备IP、用户名、密码等信息
- 设置设备类型（ALE设备使用 alcatel_aos）
- 配置各厂商的命令列表

#### 邮件配置文件 (.env)
- 配置SMTP服务器信息
- 设置发送者和接收者邮箱
- 配置邮件主题和格式

### 2. 运行程序
双击 `ALE网络运维工具包.exe` 启动程序

### 3. 使用步骤
1. 选择Excel配置文件（默认使用template.xlsx）
2. 检查邮件配置状态
3. 设置运维选项
4. 点击"开始运维"按钮
5. 查看实时日志和运维结果

## 📁 文件说明

- `ALE网络运维工具包.exe` - 主程序（包含所有运行库）
- `template.xlsx` - 设备配置模板
- `.env` - 邮件配置文件
- `README.md` - 英文说明文档
- `README_CN.md` - 中文说明文档
- `GUI使用说明.md` - GUI界面使用说明
- `启动程序.bat` - 快速启动脚本

## ⚠️ 注意事项

1. **配置文件必须与exe文件在同一目录**
2. **确保网络连接正常**
3. **检查防火墙设置**
4. **运维结果保存在LOG目录**

## 🆘 故障排除

如果程序无法启动：
1. 检查是否有杀毒软件阻止
2. 尝试以管理员身份运行
3. 确认Windows版本兼容性（支持Win7+）

如果运维失败：
1. 检查设备连接性
2. 验证用户名密码
3. 确认设备类型配置正确

---

技术支持：请查看详细的使用说明文档
'''
    
    with open(os.path.join(deployment_dir, '部署说明.md'), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 部署说明文件创建完成")

def create_startup_scripts(deployment_dir):
    """创建启动脚本"""
    # Windows批处理脚本
    bat_content = '''@echo off
chcp 65001 >nul
title ALE网络运维工具包

echo.
echo ========================================
echo    ALE网络运维工具包 v2.0
echo ========================================
echo.

echo 正在启动程序...
start "" "ALE网络运维工具包.exe"

echo 程序已启动！
echo 如果程序没有显示，请检查是否被杀毒软件阻止。
echo.
pause
'''
    
    with open(os.path.join(deployment_dir, '启动程序.bat'), 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    print("✓ 启动脚本创建完成")

def main():
    """主函数"""
    success = create_deployment_package()
    
    if success:
        print("\n" + "=" * 60)
        print("部署包创建成功！")
        print("=" * 60)
        print("\n使用方法：")
        print("1. 将整个部署包文件夹复制到目标机器")
        print("2. 配置template.xlsx和.env文件")
        print("3. 双击'启动程序.bat'或直接运行exe文件")
        print("\n注意：exe文件包含所有运行库，无需安装Python环境")
    else:
        print("\n" + "=" * 60)
        print("部署包创建失败！")
        print("=" * 60)
        print("请检查错误信息并重试")

if __name__ == '__main__':
    main()
