
=====
BASIC
=====

#########################################
show hardware-info
#########################################
Chassis 1
CPU Manufacturer                  : Broadcom
CPU Model                         : ARM
Flash Manufacturer                : Micron Technology
Flash size                        : 1997967360 bytes
RAM size                          : 2017184kB
FPGA version                      : 0.9
U-Boot Version                    : ********.R01
Power Supplies Present            : 1,-
NIs Present                       : 1,-



#########################################
show chassis
#########################################
Local Chassis ID 1 (Master)
  Model Name:                    OS6860E-24,
  Module Type:                   0x6062203,
  Description:                   Chassis,
  Part Number:                   903708-90,
  Hardware Revision:             18,
  Serial Number:                 U3983536,
  Manufacture Date:              Sep 24 2016,
  Admin Status:                  POWER ON,
  Operational Status:            UP,
  MAC Address:                   2c:fa:a2:62:9d:21



#########################################
show module long
#########################################
Module in slot CMM-A
  Model Name:                    OS6860E-24,
  Module Type:                   0x6062203,
  Description:                   24 G 4 10G,
  Part Number:                   903708-90,
  Hardware Revision:             18,
  Serial Number:                 U3983536,
  Manufacture Date:              Sep 24 2016,
  FPGA 1:                        0.9
  Admin Status:                  POWER ON,
  Operational Status:            UP,
  Max Power:                     48,
  CPU Model Type:                ,
  MAC Address:                   2c:fa:a2:62:9d:21,

Module in chassis 1 slot 1
  Model Name:                    OS6860E-24,
  Module Type:                   0x6062203,
  Description:                   24 G 4 10G,
  Part Number:                   903708-90,
  Hardware Revision:             18,
  Serial Number:                 U3983536,
  Manufacture Date:              Sep 24 2016,
  FPGA 1:                        0.9
  Admin Status:                  POWER ON,
  Operational Status:            UP,
  Max Power:                     48,
  CPU Model Type:                N/A,
  MAC Address:                   2c:fa:a2:62:9d:28,
  UBOOT Version:                 ********.R01



#########################################
show transceivers
#########################################
Chassis ID 1 
Slot 1 Transceiver 26
  ALU Model Name:                SFP-10G-SR     ,
  ALU Model Number:              903437-90   ,
  Hardware Revision:             1   ,
  Serial Number:                 CH07KC188       ,
  Manufacture Date:              Feb  9 2017,
  Laser Wave Length:             850nm,
  Admin Status:                  POWER ON,
  Operational Status:            UP

Slot 1 Transceiver 27
  ALU Model Name:                SFP-10G-SR     ,
  ALU Model Number:              903437-90   ,
  Hardware Revision:             1   ,
  Serial Number:                 CH07KC17Q       ,
  Manufacture Date:              Feb  9 2017,
  Laser Wave Length:             850nm,
  Admin Status:                  POWER ON,
  Operational Status:            UP



#########################################
show fan
#########################################
Chassis/Tray | Fan | Functional
-------------+-----+------------

This product has no fans.

#########################################
show powersupply
#########################################
             Total     PS
Chassis/PS   Power     Type     Status        Location
-----------+---------+--------+-------------+-----------
 1/1         150       AC       UP            Internal
    Total   150 


#########################################
show temperature
#########################################
Chassis/Device | Current |    Range    | Danger | Thresh |  Status
---------------+---------+-------------+--------+--------+------------
 1/CMMA            54       15 to 70      76       70     UNDER THRESHOLD


#########################################
show system
#########################################
System:
  Description:  Alcatel-Lucent Enterprise OS6860E-24 8.10.93.R03 GA, June 14, 2025.,
  Object ID:    *******.4.1.6486.801.*******.11.1.5,
  Up Time:      4 days 11 hours 7 minutes and 8 seconds,
  Contact:      Alcatel-Lucent Enterprise, https://www.al-enterprise.com,
  Name:         OS6860E-24,
  Location:     GZ-Smart City,
  Services:     78,
  Date & Time:  WED JUL 16 2025 09:47:36 (ZP8)
Flash Space:
    Primary CMM:
      Available (bytes):  1177260032,
      Comments         :  None


#########################################
show running-directory
#########################################

CONFIGURATION STATUS
  Running CMM              : MASTER-PRIMARY,
  CMM Mode                 : VIRTUAL-CHASSIS MONO CMM,
  Current CMM Slot         : CHASSIS-1 A,
  Running configuration    : WORKING,
  Certify/Restore Status   : CERTIFIED
SYNCHRONIZATION STATUS
  Running Configuration    : SYNCHRONIZED


#########################################
show microcode certified
#########################################
   /flash/certified
   Package           Release                 Size     Description
-----------------+-------------------------+---------+-----------------------------------
Uos.img           8.10.93.R03               178291978 Alcatel-Lucent OS


#########################################
show microcode working
#########################################
   /flash/working
   Package           Release                 Size     Description
-----------------+-------------------------+---------+-----------------------------------
Uos.img           8.10.93.R03               178291978 Alcatel-Lucent OS


#########################################
show microcode loaded
#########################################
   /flash/working
   Package           Release                 Size     Description
-----------------+-------------------------+---------+-----------------------------------
Uos.img           8.10.93.R03               178291978 Alcatel-Lucent OS


#########################################
show license-info
#########################################
                                             Time (Days)       Upgrade      Expiration  
VC   device   License            Type        Remaining         Status       Date        
----+------+---------------+---------------+---------------+--------------+----------------
1       <USER>    <GROUP>           PERM           NA             NA             NA              


#########################################
show lldp remote-system
#########################################
Remote LLDP nearest-bridge Agents on Local Port 1/1/7:

    Chassis dc:08:56:66:45:40, Port dc:08:56:66:45:40:
      Remote ID                   = 9,
      Chassis Subtype             = 4 (MAC Address),
      Port Subtype                = 3 (MAC address),
      Port Description            = Alcatel-Lucent Enterprise OAW-AP1301 eth1,
      System Name                 = AP-45:40,
      System Description          = Alcatel-Lucent Enterprise OAW-AP1301 ********,
      Capabilities Supported      = Bridge WLAN AP Router Station Only,
      Capabilities Enabled        = Bridge WLAN AP Router,
      Management IP Address       = ************,
      MED Device Type             = Network Connectivity,
      MED Capabilities            = Capabilities | Power via MDI-PD(33),
      MED Extension TLVs Present  = Network Policy| Inventory,
      Remote port MAC/PHY AutoNeg = Supported Enabled Capability 0x6cc1,
      Mau Type                    = 100BaseTXFD - 2 pair category 5 UTP full duplex mode

Remote LLDP nearest-bridge Agents on Local Port 1/1/9:

    Chassis 30:43:d7:ef:59:79, Port 30:43:d7:ef:59:79:
      Remote ID                   = 16,
      Chassis Subtype             = 4 (MAC Address),
      Port Subtype                = 3 (MAC address),
      Port Description            = (null),
      System Name                 = (null),
      System Description          = (null),
      Capabilities Supported      = none supported,
      Capabilities Enabled        = none enabled,
      MED Device Type             = Endpoint Class I,
      MED Capabilities            = Capabilities (01),
      Remote port MAC/PHY AutoNeg = Supported Enabled Capability 0x0001,
      Mau Type                    = Not set

Remote LLDP nearest-bridge Agents on Local Port 1/1/13:

    Chassis 22:30:5c:08:7a:a3, Port 22:30:5c:08:7a:a3:
      Remote ID                   = 8,
      Chassis Subtype             = 4 (MAC Address),
      Port Subtype                = 3 (MAC address),
      Port Description            = (null),
      System Name                 = (null),
      System Description          = (null),
      Capabilities Supported      = none supported,
      Capabilities Enabled        = none enabled,
      MED Device Type             = Endpoint Class I,
      MED Capabilities            = Capabilities (01),
      Remote port MAC/PHY AutoNeg = Supported Enabled Capability 0x0001,
      Mau Type                    = Not set

Remote LLDP nearest-bridge Agents on Local Port 1/1/19:

    Chassis dc:08:56:3b:19:00, Port dc:08:56:3b:19:00:
      Remote ID                   = 6,
      Chassis Subtype             = 4 (MAC Address),
      Port Subtype                = 3 (MAC address),
      Port Description            = Alcatel-Lucent Enterprise OAW-AP1201HL eth0-4094,
      System Name                 = AP-19:00,
      System Description          = Alcatel-Lucent Enterprise OAW-AP1201HL ********,
      Capabilities Supported      = Bridge WLAN AP Router Station Only,
      Capabilities Enabled        = Bridge WLAN AP Router,
      Management IP Address       = ************,
      MED Device Type             = Network Connectivity,
      MED Capabilities            = Capabilities | Power via MDI-PD(33),
      MED Extension TLVs Present  = Network Policy| Inventory,
      MED Power Type              = PD Device,
      MED Power Source            = PSE and Local,
      MED Power Priority          = Low,
      MED Power Value             = 25.4 W

Remote LLDP nearest-bridge Agents on Local Port 1/1/23:

    Chassis f8:3e:95:32:db:61, Port GE1/0/1:
      Remote ID                   = 5,
      Chassis Subtype             = 4 (MAC Address),
      Port Subtype                = 5 (Interface name),
      Port Description            = (null),
      System Name                 = HUAWEI,
      System Description          = Huawei Switch
Huawei YunShan OS
Version ******** (S5700 V600R022C10SPC500)
Copyright (C) 2021-2023 Huawei Technologies Co., Ltd.
HUAWEI CloudEngine S5735-S-V2
,
      Capabilities Supported      = Bridge Router,
      Capabilities Enabled        = Bridge Router,
      Management IP Address       = *******,
      Remote port default vlan    = 1,
      Vlan ID                     = 1,
      Vlan Name                   = VLAN1,
      Protocol vlan Id            = 0 (Flags = 0),
      Remote port MAC/PHY AutoNeg = Supported Enabled Capability 0x0000,
      Mau Type                    = 1000BaseXFD - PCS/PMA, unknown PMD full duplex mode

Remote LLDP nearest-bridge Agents on Local Port 1/1/27:

    Chassis 94:24:e1:83:73:c1, Port 1010:
      Remote ID                   = 1,
      Chassis Subtype             = 4 (MAC Address),
      Port Subtype                = 7 (Locally assigned),
      Port Description            = Alcatel-Lucent Enterprise OS2260-10 GNI 1/1/10,
      System Name                 = OS2260-V11,
      System Description          = Alcatel-Lucent Enterprise OS2260-10 5.2.7.R07 GA, September 23, 2024.,
      Capabilities Supported      = Bridge Router,
      Capabilities Enabled        = Bridge Router,
      Management IP Address       = ************



#########################################
show aaa authentication
#########################################
Service type = Default
  1st authentication server  = local
  Authentication exit-on-fail: Enabled
Service type = Console
  1st authentication server  = local
  Authentication exit-on-fail: Enabled
Service type = Telnet
  1st authentication server  = local
  Authentication exit-on-fail: Enabled
Service type = Ftp
  Authentication = Use Default,
  1st authentication server  = local
  Authentication exit-on-fail: Enabled
Service type = Http
  1st authentication server  = local
  Authentication exit-on-fail: Enabled
Service type = Snmp
  1st authentication server  = local
  Authentication exit-on-fail: Enabled
Service type = Ssh
  Authentication = Use Default,
  1st authentication server  = local
  Authentication exit-on-fail: Enabled


#########################################
show health
#########################################
CMM                    Current   1 Min    1 Hr   1 Day
Resources                         Avg      Avg     Avg
----------------------+---------+-------+-------+-------
CPU                     16       15      14      13
Memory                  62       62      62      62



#########################################
show health all cpu
#########################################
CPU                  Current    1 Min    1 Hr    1 Day
                                Avg      Avg     Avg
-------------------+----------+--------+-------+--------
Slot  1/1             15         14       14       13


#########################################
show vlan
#########################################
 vlan    type   admin   oper    ip    mtu          name
------+-------+-------+------+------+------+------------------
1      <USER>       <GROUP>     Ena   Ena    1500    VLAN 1                          
493    std       Ena     Dis   Dis    1500    VLAN 493                        
593    std       Ena     Dis   Dis    1500    VLAN 593                        
4094   vcm       Ena     Dis   Dis    1500    VCM IPC                         


#########################################
show spantree
#########################################
  Spanning Tree Path Cost Mode : AUTO
 Vlan STP Status Protocol Priority
-----+----------+--------+--------------
    1      <USER>       <GROUP>   32768 (0x8000)
  493      ON       RSTP   32768 (0x8000)
  593      ON       RSTP   32768 (0x8000)
 4094     OFF       RSTP   32768 (0x8000)


#########################################
show spantree ports active
#########################################
 Vlan  Port     Oper Status  Path Cost  Role   Loop Guard   Note
-----+--------+-------------+---------+-------+----------+------
    1    1/1/1    FORW             4   DESG    DIS       
    1    1/1/3    FORW             4   DESG    DIS       
    1    1/1/7    FORW            19   DESG    DIS       
    1    1/1/8    FORW             4   ROOT    DIS       
    1    1/1/9    FORW             4   DESG    DIS       
    1   1/1/13    FORW             4   DESG    DIS       
    1   1/1/17    FORW             4   DESG    DIS       
    1   1/1/19    FORW             4   DESG    DIS       
    1   1/1/23    FORW             4   DESG    DIS       
    1   1/1/27    FORW             2   DESG    DIS       


#########################################
show interfaces status
#########################################
 Chas/                 DETECTED-VALUES              CONFIGURED-VALUES    
 Slot/    Admin  Auto  Speed   Duplex  Pause  FEC   Speed   Duplex  Pause  FEC   Link
 Port     Status Nego  (Mbps)                 Det   (Mbps)                 Cfg   Trap  EEE
---------+------+----+--------+------+-------+----+--------+------+-------+-----+-----+---
 1/1/1       en    en    1000   Full     -     DIS   Auto    Auto     -    AUTO  en   dis
 1/1/2       en    en     -      -       -    -      Auto    Auto     -    AUTO  en   dis
 1/1/3       en    en    1000   Full     -     DIS   Auto    Auto     -    AUTO  en   dis
 1/1/4       en    en     -      -       -    -      Auto    Auto     -    AUTO  en   dis
 1/1/5       en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/6       en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/7       en    en     100   Full     -     DIS   Auto    Auto     -    AUTO  en   en
 1/1/8       en    en    1000   Full     -     DIS   Auto    Auto     -    AUTO  en   en
 1/1/9       en    en    1000   Full     -     DIS   Auto    Auto     -    AUTO  en   en
 1/1/10      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/11      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/12      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/13      en    en    1000   Full     -     DIS   Auto    Auto     -    AUTO  en   en
 1/1/14      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/15      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/16      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/17      en    en    1000   Full     -     DIS   Auto    Auto     -    AUTO  en   en
 1/1/18      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/19      en    en    1000   Full     -     DIS   Auto    Auto     -    AUTO  en   en
 1/1/20      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/21      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/22      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/23      en    en    1000   Full     -     DIS   Auto    Auto     -    AUTO  en   en
 1/1/24      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   en
 1/1/25      en   dis     -      -       -    -     10000    Full     -    AUTO  en   dis
 1/1/26      en   dis     -      -       -    -     10000    Full     -    AUTO  en   dis
 1/1/27      en   dis   10000   Full     -     DIS  10000    Full     -    AUTO  en   dis
 1/1/28      en   dis     -      -       -    -     10000    Full     -    AUTO  en   dis
 1/1/29      en   dis     -      -       -    -     21000    Full     -    AUTO  en   dis
 1/1/30      en   dis     -      -       -    -     21000    Full     -    AUTO  en   dis


#########################################
show interfaces counters
#########################################
1/1/1   ,
  InOctets      =         434532892717,  OutOctets      =         118875524942,
  InUcastPkts   =            383106008,  OutUcastPkts   =            394289408,
  InMcastPkts   =                    0,  OutMcastPkts   =               523672,
  InBcastPkts   =               382612,  OutBcastPkts   =               464653,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                  147,  OutPkts/s      =                  224,
  InBits/s      =               168664,  OutBits/s      =               592504
1/1/3   ,
  InOctets      =           1289521040,  OutOctets      =           1010319481,
  InUcastPkts   =              4464361,  OutUcastPkts   =              1765212,
  InMcastPkts   =                    3,  OutMcastPkts   =               523672,
  InBcastPkts   =                   10,  OutBcastPkts   =               847253,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                    2,  OutPkts/s      =                    4,
  InBits/s      =                12704,  OutBits/s      =                 2472
1/1/7   ,
  InOctets      =           2332105490,  OutOctets      =          23004895867,
  InUcastPkts   =              8650395,  OutUcastPkts   =             21476312,
  InMcastPkts   =                42044,  OutMcastPkts   =               493409,
  InBcastPkts   =               195599,  OutBcastPkts   =               651539,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                   10,  OutPkts/s      =                    8,
  InBits/s      =                 6088,  OutBits/s      =                 5408
1/1/8   ,
  InOctets      =          69451351297,  OutOctets      =         116704434980,
  InUcastPkts   =             85013790,  OutUcastPkts   =            100395003,
  InMcastPkts   =               276241,  OutMcastPkts   =               247484,
  InBcastPkts   =                 3267,  OutBcastPkts   =               844022,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                    4,  OutPkts/s      =                    5,
  InBits/s      =                 2904,  OutBits/s      =                 3064
1/1/9   ,
  InOctets      =          22141130820,  OutOctets      =          60153636475,
  InUcastPkts   =             49837093,  OutUcastPkts   =             57419852,
  InMcastPkts   =                35046,  OutMcastPkts   =               489133,
  InBcastPkts   =                 5095,  OutBcastPkts   =               842160,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                   62,  OutPkts/s      =                   38,
  InBits/s      =               123560,  OutBits/s      =                59840
1/1/13  ,
  InOctets      =           9706330324,  OutOctets      =          10958307429,
  InUcastPkts   =             13600807,  OutUcastPkts   =             14068121,
  InMcastPkts   =                 2528,  OutMcastPkts   =               521608,
  InBcastPkts   =               153944,  OutBcastPkts   =               693330,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                   15,  OutPkts/s      =                   10,
  InBits/s      =                16368,  OutBits/s      =                17688
1/1/17  ,
  InOctets      =           1325336862,  OutOctets      =          15908234470,
  InUcastPkts   =              9854794,  OutUcastPkts   =             16275429,
  InMcastPkts   =                 4037,  OutMcastPkts   =               519640,
  InBcastPkts   =                43444,  OutBcastPkts   =               803834,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                   10,  OutPkts/s      =                   14,
  InBits/s      =                 8056,  OutBits/s      =                10328
1/1/19  ,
  InOctets      =            104764174,  OutOctets      =            243600717,
  InUcastPkts   =               658521,  OutUcastPkts   =               583053,
  InMcastPkts   =                16155,  OutMcastPkts   =               520363,
  InBcastPkts   =                 3569,  OutBcastPkts   =               843692,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                    2,  OutPkts/s      =                    6,
  InBits/s      =                 2448,  OutBits/s      =                 3712
1/1/23  ,
  InOctets      =             79498417,  OutOctets      =            158675307,
  InUcastPkts   =               227149,  OutUcastPkts   =               391137,
  InMcastPkts   =                16108,  OutMcastPkts   =               578039,
  InBcastPkts   =                99127,  OutBcastPkts   =               417033,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                    0,  OutPkts/s      =                    6,
  InBits/s      =                    0,  OutBits/s      =                 3712
1/1/27  ,
  InOctets      =         215060941823,  OutOctets      =         410484738710,
  InUcastPkts   =            431336305,  OutUcastPkts   =            380076476,
  InMcastPkts   =               133606,  OutMcastPkts   =               428264,
  InBcastPkts   =                54069,  OutBcastPkts   =               793248,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                  145,  OutPkts/s      =                  108,
  InBits/s      =               445960,  OutBits/s      =                95816


#########################################
show ip interface
#########################################
Total 9 interfaces
 Flags (D=Directly-bound)
       (A=Anycast IP)

            Name                 IP Address      Subnet Mask     Status Forward  Device              Flags
--------------------------------+---------------+---------------+------+-------+--------------------+------
EMP-CHAS1                        *************   *************   DOWN   NO      EMP                       
EMP-CMMA-CHAS1                   0.0.0.0         0.0.0.0         DOWN   NO      EMP                       
Loopback                         127.0.0.1       *************** UP     NO      Loopback                  
Loopback0                        *******         *************** UP     YES     Loopback0                 
no                               0.0.0.0         0.0.0.0         DOWN   NO      unbound                   
vlan1                            ***********     *************   UP     YES     vlan 1                    
vlan10                           **************  *************   DOWN   NO      vlan 10                   
vlan30                           *************   *************   DOWN   NO      vlan 30                   
vlan40                           *************   *************   DOWN   NO      vlan 40                   


#########################################
show ip config
#########################################

IP directed-broadcast   =   OFF,
IP default TTL          =   64
Distributed ARP         =   OFF,
Anycast MAC             =   00:00:00:00:00:00,
Proxy-arp aging-time    =   300



#########################################
show ip protocols
#########################################
IP Protocols
RIP status				= Not Loaded,
OSPF status				= Loaded,
ISIS status				= Not Loaded,
BGP status				= Not Loaded,
PIM status				= Not Loaded,
DVMRP status				= Not Loaded,
RIPng status				= Not Loaded,
OSPF3 status				= Not Loaded,
LDP status				= Not Loaded,
VRRP status				= Loaded,

#########################################
show ip dos statistics
#########################################

  DoS type                             Attacks detected  
----------------------------------+--------------------------
  port scan                                           0
  ping of death                                       0
  land                                                0
  loopback-src                                        0
  invalid-ip                                          0
  invalid-multicast                                   0
  unicast dest-ip/multicast-mac                       0
  ping overload                                       0
  arp flood                                           0
  arp poison                                          0
  anti-spoof                                          0
  gratuitous-arp                                      0
  ip-options-filter                                   0


#########################################
show snmp statistics
#########################################
From RFC1907
  snmpInPkts                                   = 185251
  snmpOutPkts                                  = 185251
  snmpInBadVersions                            = 0
  snmpInBadCommunityNames                      = 0
  snmpInBadCommunityUses                       = 0
  snmpInASNParseErrs                           = 0
  snmpEnableAuthenTraps                        = disabled(2)
  snmpSilentDrop                               = 0
  snmpProxyDrops                               = 0
  snmpInTooBigs                                = 0
  snmpInNoSuchNames                            = 0
  snmpInBadValues                              = 0
  snmpInReadOnlys                              = 0
  snmpInGenErrs                                = 0
  snmpInTotalReqVars                           = 3144903
  snmpInTotalSetVars                           = 3
  snmpInGetRequests                            = 39288
  snmpInGetNexts                               = 96084
  snmpInSetRequests                            = 2 
  snmpInGetResponses                           = 0
  snmpInTraps                                  = 0
  snmpOutTooBigs                               = 0
  snmpOutNoSuchNames                           = 0
  snmpOutBadValues                             = 0
  snmpOutGenErrs                               = 0
  snmpOutGetRequests                           = 0
  snmpOutGetNexts                              = 0
  snmpOutSetRequests                           = 0
  snmpOutGetResponses                          = 185251
  snmpOutTraps                                 = 0
From RFC2572
  snmpUnknownSecurityModels                    = 0
  snmpInvalidMsgs                              = 0
  snmpUnknownPDUHandlers                       = 0
From RFC2573
  snmpUnavailableContexts                      = 0
  snmpUnknownContexts                          = 0
From RFC2574
  usmStatsUnsupportedSecLevels                 = 0 
  usmStatsNotInTimeWindows                     = 0
  usmStatsUnknownUserNames                     = 0
  usmStatsUnknownEngineIDs                     = 0
  usmStatsWrongDigests                         = 0
  usmStatsDecryptionErrors                     = 0 
From RFC5591
  snmpTsmInvalidCaches                         = 0 
  snmpTsmInadequateSecurityLevels              = 0 
  snmpTsmUnknownPrefixes                       = 0 
  snmpTsmInvalidPrefixes                       = 0 
From RFC5953
  snmpTlstmSessionOpens                        = 0 
  snmpTlstmSessionClientCloses                 = 0 
  snmpTlstmSessionOpenErrors                   = 0 
  snmpTlstmSessionAccepts                      = 0 
  snmpTlstmSessionServerCloses                 = 0 
  snmpTlstmSessionNoSessions                   = 0 
  snmpTlstmSessionInvalidClientCertificates    = 0 
  snmpTlstmSessionUnknownServerCertificate     = 0 
  snmpTlstmSessionInvalidServerCertificates    = 0 
  snmpTlstmSessionInvalidCaches                = 0 
From RFC3411
  snmpEngineID                                 = 80001956032cfaa2629d21 
  snmpEngineBoots                              = 67 
  snmpEngineTime                               = 385504 
  snmpEngineMaxMessageSize                     = 1500 


#########################################
show virtual-chassis topology
#########################################
Legend: Status suffix "+" means an added unit after last saved topology

Local Chassis: 1
 Oper                                   Config   Oper                          
 Chas  Role         Status              Chas ID  Pri   Group  MAC-Address      
-----+------------+-------------------+--------+-----+------+------------------
 1     <USER>       <GROUP>             1        100   0      2c:fa:a2:62:9d:21


#########################################
show virtual-chassis consistency
#########################################
Legend: * - denotes mandatory consistency which will affect chassis status
        licenses-info - A: Advanced; B: Data Center; M: Metro, AR: Advanced Routing, C: MACSEC;

       Config           Oper                   Oper     Config   
       Chas             Chas    Chas   Hello   Control  Control  
 Chas* ID     Status    Type*   Group* Interv  Vlan*    Vlan     License* 
------+------+---------+-------+------+-------+--------+--------+----------
 1     <USER>      <GROUP>        OS6860  0      15      4094     4094     A         



#########################################
show virtual-chassis vf-link member-port
#########################################



#########################################
show virtual-chassis chassis-reset-list
#########################################
 Chas  Chassis reset list  
-----+---------------------
 1     1,            


#########################################
show virtual-chassis slot-reset-list
#########################################
 Chas  Slot    Reset status
-----+-------+--------------
 1     <USER>       <GROUP>   


#########################################
show virtual-chassis vf-link
#########################################



#########################################
show virtual-chassis auto-vf-link-port
#########################################
 Chassis/Slot/Port  Chassis/VFLink ID  VFLink member status
-------------------+------------------+--------------------
 1/1/29             Unassigned         Unassigned          
 1/1/30             Unassigned         Unassigned          



#########################################
show virtual-chassis neighbors
#########################################

Chas VFL
 ID   0 
----+---
  1    -



#########################################
debug show virtual-chassis topology
#########################################
Legend: Status suffix "+" means an added unit after last saved topology

Local Chassis: 1
 Oper                                   Config   Oper                            System
 Chas  Role         Status              Chas ID  Pri   Group  MAC-Address        Ready 
-----+------------+-------------------+--------+-----+------+------------------+-------
 1     Master       Running             1        100   0      2c:fa:a2:62:9d:21  Yes   


#########################################
debug show virtual-chassis status
#########################################

 ID  Level  Parameter                     Value            Timestamp   Status  
----+------+-----------------------------+----------------+-----------+---------
 0   L0     Chassis Identifier            1                09:47:36    OK      
 1   L0     Designated NI Module          1                09:47:36    OK      
 2   L0     Designated NI Module (@L5)    1                22:43:40    OK      
 3   L0     License Configured            Yes              09:47:36    OK      
 4   L0     License Configured (@L5)      Yes              22:43:40    OK      
 5   L0     VFL Links Configured          0                09:47:36    NOK_07  
 6   L0     VFL Links Configured (@L5)    0                22:43:40    NOK_07  
 7   L0     VFL Ports Configured          0                09:47:36    NOK_08  
 8   L0     VFL Ports Configured (@L5)    0                22:43:40    NOK_08  
 11  L0     Chassis Ready Received        Yes              22:43:25    OK      
 12  L1     VFL Intf Oper Status          Down             09:47:36    NOK_09  
 13  L1     VFL Intf Oper Status (@L5)    Down             22:43:40    NOK_09  
 14  L2     VFL LACP Status               Down             09:47:36    NOK_14  
 15  L2     VFL LACP Status (@L5)         Down             22:43:40    NOK_14  
 16  L2     VFL LACP Up -> Down           0                N/A         N/A     
 17  L2     VFL LACP Down -> Up           0                N/A         N/A     
 18  L3     VCM Protocol Role (@L5)       Master           22:43:40    OK      
 19  L3     VCM Protocol Role             Master           09:47:36    OK      
 20  L3     VCM Protocol Status (@L5)     Running          22:43:40    OK      
 21  L3     VCM Protocol Status           Running          09:47:36    OK      
 24  L4     VCM Connection                N/A              09:47:36    N/A     
 25  L4     VCM Connection (@L5)          N/A              22:43:40    N/A     
 26  L5     VCM Synchronization           Single-node      09:47:36    NOK_17  
 27  L6     Chassis Sup Connection        N/A              N/A         N/A     
 28  L6     Remote Flash Mounted          N/A              N/A         N/A     
 29  L6     Image and Config Checked      N/A              N/A         N/A     
 30  L6     VC Takeover Sent              Yes              22:43:40    OK      
 31  L7     VC Takeover Acknowledged      Yes              22:43:41    OK      
 32  L8     System Ready Received         Yes              22:43:41    OK      
 33  L8     RCD Operational Status        Down             22:42:04    NOK_25  
 34  L8     RCD IP Address                0.0.0.0          22:42:04    NOK_26  

Error/Information Codes Detected:
--------------------------------------------------------------------------------
NOK_07
    There are no virtual-fabric links configured on this switch.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port

NOK_07
    There are no virtual-fabric links configured on this switch.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port

NOK_08
    There are no virtual-fabric member ports configured on this switch.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port configured or assigned to each of the 
    virtual-fabric links.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"

NOK_08
    There are no virtual-fabric member ports configured on this switch.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port configured or assigned to each of the 
    virtual-fabric links.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"

NOK_09
    There are no virtual-fabric member interfaces operationally up.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port interface up on each virtual-fabric link.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"
    -> show interfaces port <chassis>/<slot>/<port> status

NOK_09
    There are no virtual-fabric member interfaces operationally up.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port interface up on each virtual-fabric link.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"
    -> show interfaces port <chassis>/<slot>/<port> status

NOK_14
    The virtual-fabric links configured on this switch are not operationally up.
    If there are multiple links configured, all of them must be operationally up
    in order for this parameter to be reported as OK.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link | grep "<local-chassis-id>/"

NOK_14
    The virtual-fabric links configured on this switch are not operationally up.
    If there are multiple links configured, all of them must be operationally up
    in order for this parameter to be reported as OK.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link | grep "<local-chassis-id>/"

NOK_17
    The virtual-chassis manager protocol did not discover any peer switch
    within the discovery time window (i.e. 4 minutes) starting from the time
    we reached the chassis ready state.
    It is possible that there are no other peers, there are no virtual-fabric
    links configured or operational between the switches, or the virtual-chassis
    manager protocol packets are not going through.
    Troubleshooting Tips:
    a) Check if any peer switches were discovered using:
       -> show virtual-chassis topology
    b) If no peers were discovered, check whether the virtual-fabric links are
       operational
       -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"

NOK_25
    The RCD (Remote Chassis Detection) protocol is not running.
    There are no local EMP IP addresses configured (i.e. chassis EMP or CMM EMP)
    Troubleshooting Tips:
    -> show ip interface

NOK_26
    There is no valid IP address to be used by the RCD (Remote Chassis Detection) protocol
    Troubleshooting Tips:
    -> show ip interface



#########################################
debug show virtual-chassis connection
#########################################
                          Address           Address                       
 Chas  MAC-Address        Local IP          Remote IP         Status      
-----+------------------+-----------------+-----------------+-------------



#########################################
show cloud-agent status
#########################################
Admin State                     : Disabled,
Activation Server State         : Unknown,
Device State                    : Initial,
Error State                     : None,
Cloud Group                     : -,
DHCP Address                    : -,
DHCP IP Address Mask            : -,
Gateway                         : -,
Activation Server               : activation.myovcloud.com:443,
Network ID                      : -,
NTP Server                      : *************, *************,
DNS Server                      : -,
DNS Domain                      : -,
Proxy Server                    : -,
VPN Server                      : -,
Preprovision Server             : -,
OV Tenant                       : -,
VPN DPD Time (sec)              : 600,
Image Server                    : -,
Image Download Retry Count      : -,
Discovery Interval (min)        : 30,
Time to next Call Home (sec)    : -,
Call Home Timer Status          : Not-Running,
Discovery Retry Count           : 0,
Certificate Status              : -
Thin Client                     : Disabled
Retry Call-Home time remaining  : 999999


#########################################
show pkgmgr
#########################################
Legend: (+) indicates package is not saved across reboot
        (*) indicates packages will be installed or removed after reload
 Name                Version           Status             Install Script        
---------------+---------------------+------------------+---------------------------------
  ams           default               installed          default                       
  webview       8.6.R02-203           installed          /flash/working/pkg/webview/install.sh
  ams-apps      default               installed          default                       


#########################################
show appmgr
#########################################
Legend: (+) indicates application is not saved across reboot
  Application       Status    Package Name        User                  Status Time Stamp
------------------+---------+-------------------+---------------------+---------------------


#########################################
show naas license
#########################################
     Serial             Device           Device        Call-home   Grace    Valid        Expiry       Expiry
 VC  Number             Mode             State         Period      Period   Licenses     Day          Time
---+------------------+----------------+-------------+-----------+--------+------------+------------+-------------
 1   <USER>           <GROUP>            Licensed      N/A         N/A      N/A          N/A          N/A         


#########################################
show naas-agent status
#########################################
Proxy Server                    : N/A,
Activation Server               : license.ovng.myovcloud.com,
DNS Server                      : N/A,
DNS Domain                      : N/A,
NTP Server                      : *************, *************,
Call Home Timer Status          : Not-Running,
Time to next call-home (min)    : 0,
Grace Period (days)             : -,
Grace Period (cause)            : No license


#########################################
debug show capability naas
#########################################

+=============NAAS============+
+  VC  +  Naas  +Decided +Esntial +Advanced+   DC   +  10G   +  50G   + MACSEC +  MPLS  + GRACE  +  DEGR  + MGMT  + UPGRADE+GRC_MGMT+GRCUPGRADE+DEGMGMT+DEGUPGRADE+ADVROUTING
+------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+--------+-------+--------+--------+--------+--------+-----------+--------+
  1    |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False  |  False  |  False  |  False  |  False  |
  2    |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False  |  False  |  False  |  False  |  False  |
  3    |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False  |  False  |  False  |  False  |  False  |
  4    |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False  |  False  |  False  |  False  |  False  |
  5    |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False  |  False  |  False  |  False  |  False  |
  6    |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False  |  False  |  False  |  False  |  False  |
  7    |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False  |  False  |  False  |  False  |  False  |
  8    |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False |  False  |  False  |  False  |  False  |  False  |


#########################################
show ntp server status
#########################################
IP address         = *************,
Host mode          = client,
Peer mode          = unspec,
Prefer             = no,
Version            = 4,
Key                = 0,
Stratum            = 16,
Last Error         = invalid header or stratum, distance threshold exceeded, unreachable or nonselect,
Minpoll            = 6 (64 seconds),
Maxpoll            = 10 (1024 seconds),
Poll               = 1024 seconds,
When               = 0 seconds,
Delay              = 0.000,
Offset             = 0.000,
Dispersion         = 15937.500
Root distance      = 0.000,
Precision          = -19,
Reference IP       = XFAC,
Status             = configured : persistent association : reject,
Uptime count       = 385531 seconds,
Reachability       = 0,
Unreachable count  = 376,
Stats reset count  = 0,
Packets sent       = 376,
Packets received   = 0,
Duplicate packets  = 0,
Bogus origin       = 0,
Bad authentication = 0,
Bad dispersion     = 0,
Last Event         = association mobilized,

IP address         = *************,
Host mode          = client,
Peer mode          = unspec,
Prefer             = no,
Version            = 4,
Key                = 0,
Stratum            = 16,
Last Error         = invalid header or stratum, distance threshold exceeded, unreachable or nonselect,
Minpoll            = 6 (64 seconds),
Maxpoll            = 10 (1024 seconds),
Poll               = 1024 seconds,
When               = 0 seconds,
Delay              = 0.000,
Offset             = 0.000,
Dispersion         = 15937.500
Root distance      = 0.000,
Precision          = -19,
Reference IP       = INIT,
Status             = configured : persistent association : reject,
Uptime count       = 385531 seconds,
Reachability       = 0,
Unreachable count  = 332,
Stats reset count  = 46748,
Packets sent       = 332,
Packets received   = 0,
Duplicate packets  = 0,
Bogus origin       = 0,
Bad authentication = 0,
Bad dispersion     = 0,
Last Event         = association mobilized,



#########################################
show ntp status
#########################################
Current time:                   Wed, Jul 16 2025  9:47:36.854 (GMT-8),
Last NTP update:                -,
Server reference:               .,
Client mode:                    enabled,
Broadcast client mode:          enabled,
Broadcast delay (microseconds): 4000,
Clock status:                   unsynchronized,
Stratum:                        16,
Maximum Associations Allowed:   128,
Authentication:                 enabled,
Source IP Configured:           default,
VRF Name:                       default


#########################################
show ntp keys
#########################################
Key       Status
-------+------------


#########################################
show capability profile
#########################################
ERROR: Command not supported on this platform



#########################################
show license-info
#########################################
                                             Time (Days)       Upgrade      Expiration  
VC   device   License            Type        Remaining         Status       Date        
----+------+---------------+---------------+---------------+--------------+----------------
1       <USER>    <GROUP>           PERM           NA             NA             NA              


#########################################
show license-server
#########################################
ERROR: specified application not loaded


#########################################
show license-server info
#########################################
ERROR: specified application not loaded


#########################################
show license-server usage
#########################################
ERROR: specified application not loaded


#########################################
show license-client info
#########################################
ERROR: specified application not loaded

