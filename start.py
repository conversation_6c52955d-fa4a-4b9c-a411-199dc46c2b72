#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
网络设备巡检系统启动脚本
提供快速启动和初始化功能
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True


def check_dependencies():
    """检查依赖库"""
    required_packages = [
        'netmiko',
        'openpyxl', 
        'prettytable',
        'pandas'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖库:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def check_template_file():
    """检查模板文件"""
    template_file = "巡检模板.xlsx"
    if not os.path.exists(template_file):
        print(f"模板文件不存在: {template_file}")
        choice = input("是否创建模板文件? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            try:
                from create_template import create_inspection_template
                create_inspection_template()
                return True
            except Exception as e:
                print(f"创建模板文件失败: {e}")
                return False
        else:
            print("请手动创建模板文件或运行: python create_template.py")
            return False
    return True


def install_dependencies():
    """安装依赖库"""
    try:
        print("正在安装依赖库...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖库安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装依赖库失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("网络设备自动化巡检系统")
    print("Network Device Inspection System")
    print("=" * 60)
    
    # 检查Python版本
    print("1. 检查Python版本...")
    if not check_python_version():
        return
    print("   ✓ Python版本检查通过")
    
    # 检查依赖库
    print("2. 检查依赖库...")
    if not check_dependencies():
        choice = input("是否自动安装依赖库? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            if not install_dependencies():
                return
            # 重新检查
            if not check_dependencies():
                return
        else:
            return
    print("   ✓ 依赖库检查通过")
    
    # 检查模板文件
    print("3. 检查配置模板...")
    if not check_template_file():
        return
    print("   ✓ 配置模板检查通过")
    
    print("\n" + "=" * 60)
    print("系统检查完成，准备启动...")
    print("=" * 60)
    
    # 启动主程序
    try:
        from network_inspection import main as inspection_main
        inspection_main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
