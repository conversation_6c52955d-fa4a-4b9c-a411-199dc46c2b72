# 网络设备巡检系统使用示例

## 概述

本系统基于您现有的模块进行整合，支持通过Excel配置SSH用户名密码和netmiko厂商名称，每个sheet对应不同厂商的特定命令。

## 快速开始（虚拟环境）

### 1. 一键设置
```bash
# 在虚拟环境中运行
python quick_setup.py
```
这个脚本会自动：
- 检查虚拟环境
- 安装所需依赖
- 创建Excel模板
- 测试模块导入

### 2. 手动设置
```bash
# 安装依赖
pip install netmiko openpyxl prettytable pandas paramiko

# 创建模板
python create_simple_template.py

# 运行巡检
python simple_inspection.py
```

## Excel模板配置

### 设备信息工作表
Excel文件包含SSH用户名密码和netmiko厂商名称：

| 序号 | 状态 | 设备IP | 协议 | 端口 | 用户名 | 密码 | 特权密码 | 设备类型 |
|------|------|--------|------|------|--------|------|----------|----------|
| 1 | 启用 | *********** | ssh | 22 | admin | password | | cisco_ios |
| 2 | 启用 | *********** | ssh | 22 | admin | password | enable_pass | cisco_ios |
| 3 | # | *********** | telnet | 23 | admin | password | | huawei |
| 4 | 启用 | *********** | ssh | 22 | admin | password | | alcatel_aos |

**说明：**
- 状态列填写"#"表示跳过该设备
- 设备类型使用netmiko支持的厂商名称
- 用户名密码直接配置在Excel中

### 厂商特定命令工作表

每个设备类型对应一个工作表，配置该厂商的特定命令：

#### cisco_ios工作表
| 状态 | 命令 |
|------|------|
| 启用 | show version |
| 启用 | show running-config |
| 启用 | show interface status |
| 启用 | show ip interface brief |
| 启用 | show vlan brief |
| # | show tech-support |

#### huawei工作表
| 状态 | 命令 |
|------|------|
| 启用 | display version |
| 启用 | display current-configuration |
| 启用 | display interface brief |
| 启用 | display vlan |
| # | display diagnostic-information |

#### alcatel_aos工作表
| 状态 | 命令 |
|------|------|
| 启用 | show system |
| 启用 | show configuration snapshot |
| 启用 | show interfaces status |
| 启用 | show vlan |
| 启用 | show health |

## 支持的netmiko设备类型

| 厂商 | netmiko设备类型 | 说明 |
|------|----------------|------|
| Cisco | cisco_ios | Cisco IOS设备 |
| 华为 | huawei | 华为设备 |
| ALE | alcatel_aos | ALE OmniSwitch |
| 锐捷 | ruijie_os | 锐捷设备 |
| H3C | hp_comware | H3C设备 |
| Juniper | juniper_junos | Juniper设备 |

## 使用方法

### 1. 交互式模式
```bash
python simple_inspection.py
```
选择功能菜单：
1. 设备连接测试
2. 设备信息采集
3. 完整巡检流程
4. 压缩现有结果
5. 发送邮件通知

### 2. 命令行模式
```bash
# 完整巡检流程
python simple_inspection.py --auto

# 仅连接测试
python simple_inspection.py --test

# 仅信息采集
python simple_inspection.py --collect
```

### 3. 使用原始模块
```bash
# 直接使用connect.py
python connect.py

# 选择功能
# 1. 连接测试
# 2. 采集设备信息
```

## 输出结果

### 目录结构
```
LOG/
├── YYYYMMDD_HHMMSS/           # 按时间戳分组
│   ├── ***********_Router01/  # 按设备IP和主机名分组
│   │   ├── show version.conf
│   │   ├── show running-config.conf
│   │   └── ...
│   └── ***********_Switch01/
│       └── ...
├── error_YYYYMMDD_HHMMSS.log  # 错误日志
└── connect_t_YYYYMMDD_HHMMSS.log  # 连接测试日志
```

### 压缩文件
- `inspection_results_YYYYMMDD_HHMMSS.zip` - 完整结果压缩包

## 配置示例

### 1. 混合厂商环境
```
设备信息工作表:
***********  cisco_ios    (Cisco核心交换机)
***********  huawei       (华为接入交换机)
***********  alcatel_aos  (ALE汇聚交换机)
***********  ruijie_os    (锐捷边缘交换机)
```

### 2. SSH和Telnet混合
```
***********  ssh   22  admin  password      cisco_ios
***********  ssh   22  admin  password      huawei
***********  telnet 23  admin  password      alcatel_aos
```

### 3. 特权密码配置
```
设备IP        用户名  密码      特权密码     设备类型
***********   admin   password  enable123    cisco_ios
***********   admin   password               huawei
```

## 高级配置

### 1. 调整并发数
在 `connect.py` 中修改：
```python
self.pool = ThreadPool(10)  # 调整并发线程数
```

### 2. 超时设置
```python
# 华为设备特殊超时设置
if 'huawei' in host['device_type']:
    connect = ConnectHandler(**host, conn_timeout=15)
else:
    connect = ConnectHandler(**host)
```

### 3. 邮件配置
编辑 `send_email.py`：
```python
sender = '发送者邮箱'
receiver = '接受者邮箱'
# 配置SMTP服务器
```

## 故障排除

### 1. 模块导入错误
```bash
# 检查虚拟环境
python -c "import sys; print(sys.prefix)"

# 安装缺失模块
pip install netmiko openpyxl
```

### 2. 连接失败
- 检查设备IP和端口
- 确认用户名密码
- 检查网络连通性
- 验证SSH/Telnet服务状态

### 3. 命令执行失败
- 检查设备类型配置
- 确认命令语法
- 验证用户权限

### 4. Excel文件问题
```bash
# 重新创建模板
python create_simple_template.py

# 检查文件格式
python -c "from openpyxl import load_workbook; wb = load_workbook('template.xlsx'); print(wb.sheetnames)"
```

## 最佳实践

1. **设备分组**: 按网络区域或设备类型分组配置
2. **命令优化**: 根据实际需求调整巡检命令
3. **并发控制**: 根据网络环境调整并发数
4. **日志管理**: 定期清理旧的日志文件
5. **安全考虑**: 保护包含密码的Excel文件

## 扩展功能

1. **添加新厂商**: 在Excel中创建新的设备类型工作表
2. **自定义命令**: 根据需求修改各厂商的巡检命令
3. **结果分析**: 基于LOG文件进行进一步的数据分析
4. **定时任务**: 结合cron或Windows任务计划器实现定时巡检

---

**注意**: 请在生产环境使用前充分测试，确保配置正确且符合安全要求。
