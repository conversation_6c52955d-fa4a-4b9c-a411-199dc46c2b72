#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
网络设备巡检主程序
整合各个功能模块，完成完整的交换机巡检流程
支持SSH用户名密码配置和多厂商设备类型
"""

import os
import sys
import logging
import shutil
from datetime import datetime

# 检查并导入现有的功能模块
try:
    from connect import BackupConfig
    CONNECT_AVAILABLE = True
except ImportError:
    CONNECT_AVAILABLE = False
    print("警告: connect.py模块不可用")

try:
    from send_email import send
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False
    print("警告: send_email.py模块不可用")

try:
    from zip_file import compress_zip
    ZIP_AVAILABLE = True
except ImportError:
    ZIP_AVAILABLE = False
    print("警告: zip_file.py模块不可用")


class NetworkInspection:
    """网络设备巡检主类"""
    
    def __init__(self):
        """初始化巡检参数"""
        self.logtime = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        self.log_dir = "LOG"
        self.logger = None

        # 检查并初始化备份配置模块
        if CONNECT_AVAILABLE:
            self.backup_config = BackupConfig()
            # 检查模板文件是否存在
            if not os.path.exists(self.backup_config.device_file):
                print(f"警告: 模板文件 {self.backup_config.device_file} 不存在")
                print("请运行 python create_simple_template.py 创建模板文件")
        else:
            self.backup_config = None
            print("错误: 无法加载connect模块，请检查connect.py文件")

        self.inspection_results = {
            'total_devices': 0,
            'success_devices': [],
            'failed_devices': [],
            'start_time': None,
            'end_time': None,
            'log_files': []
        }
        
    def setup_logging(self):
        """设置日志记录"""
        log_file = f"inspection_{self.logtime}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        return log_file
        
    def print_banner(self):
        """打印程序横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    网络设备自动化巡检系统                      ║
║                  Network Device Inspection System            ║
╠══════════════════════════════════════════════════════════════╣
║  功能：                                                      ║
║  1. 批量连接测试                                             ║
║  2. 设备信息采集                                             ║
║  3. 巡检报告生成                                             ║
║  4. 结果打包压缩                                             ║
║  5. 邮件发送通知                                             ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        
    def connection_test(self):
        """执行连接测试"""
        if not self.backup_config:
            print("错误: 备份配置模块不可用")
            return False

        if self.logger:
            self.logger.info("开始执行设备连接测试...")
        print("=" * 80)
        print("正在执行设备连接测试...")
        print("=" * 80)

        try:
            # 执行连接测试
            self.backup_config.connect_t()

            # 收集测试结果
            self.inspection_results['success_devices'] = self.backup_config.success.copy()
            self.inspection_results['failed_devices'] = self.backup_config.fail.copy()
            self.inspection_results['total_devices'] = len(self.inspection_results['success_devices'] +
                                                         self.inspection_results['failed_devices'])

            if self.logger:
                self.logger.info(f"连接测试完成 - 成功: {len(self.inspection_results['success_devices'])}, "
                               f"失败: {len(self.inspection_results['failed_devices'])}")

            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"连接测试失败: {str(e)}")
            print(f"连接测试失败: {str(e)}")
            return False
            
    def device_inspection(self):
        """执行设备信息采集"""
        if not self.backup_config:
            print("错误: 备份配置模块不可用")
            return False

        if self.logger:
            self.logger.info("开始执行设备信息采集...")
        print("=" * 80)
        print("正在执行设备信息采集...")
        print("=" * 80)

        try:
            # 重置计数器
            self.backup_config.success = []
            self.backup_config.fail = []

            # 执行设备信息采集
            self.backup_config.connect()

            # 更新结果
            self.inspection_results['success_devices'] = self.backup_config.success.copy()
            self.inspection_results['failed_devices'] = self.backup_config.fail.copy()
            self.inspection_results['total_devices'] = len(self.inspection_results['success_devices'] +
                                                         self.inspection_results['failed_devices'])

            if self.logger:
                self.logger.info(f"设备信息采集完成 - 成功: {len(self.inspection_results['success_devices'])}, "
                               f"失败: {len(self.inspection_results['failed_devices'])}")

            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"设备信息采集失败: {str(e)}")
            print(f"设备信息采集失败: {str(e)}")
            return False
            
    def generate_summary_report(self):
        """生成巡检汇总报告"""
        try:
            report_file = os.path.join(self.log_dir, f"inspection_summary_{self.logtime}.txt")
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("网络设备巡检汇总报告\n")
                f.write("=" * 80 + "\n")
                f.write(f"巡检时间: {self.logtime}\n")
                f.write(f"设备总数: {self.inspection_results['total_devices']}\n")
                f.write(f"成功设备数: {len(self.inspection_results['success_devices'])}\n")
                f.write(f"失败设备数: {len(self.inspection_results['failed_devices'])}\n")
                f.write("\n")
                
                if self.inspection_results['success_devices']:
                    f.write("成功设备列表:\n")
                    f.write("-" * 40 + "\n")
                    for device in self.inspection_results['success_devices']:
                        f.write(f"  ✓ {device}\n")
                    f.write("\n")
                
                if self.inspection_results['failed_devices']:
                    f.write("失败设备列表:\n")
                    f.write("-" * 40 + "\n")
                    for device in self.inspection_results['failed_devices']:
                        f.write(f"  ✗ {device}\n")
                    f.write("\n")
                
                f.write("=" * 80 + "\n")
                f.write("巡检完成\n")
                f.write("=" * 80 + "\n")
            
            self.inspection_results['log_files'].append(report_file)
            self.logger.info(f"巡检汇总报告已生成: {report_file}")
            return report_file
            
        except Exception as e:
            self.logger.error(f"生成汇总报告失败: {str(e)}")
            return None
            
    def compress_results(self):
        """压缩巡检结果"""
        try:
            # 创建压缩文件名
            zip_filename = f"inspection_results_{self.logtime}.zip"
            
            # 压缩LOG目录
            if os.path.exists(self.log_dir):
                compress_zip(self.log_dir, zip_filename)
                self.logger.info(f"巡检结果已压缩: {zip_filename}")
                return zip_filename
            else:
                self.logger.warning("LOG目录不存在，跳过压缩")
                return None
                
        except Exception as e:
            self.logger.error(f"压缩结果失败: {str(e)}")
            return None

    def send_notification(self, zip_file=None):
        """发送邮件通知"""
        try:
            self.logger.info("准备发送邮件通知...")

            # 这里可以调用send_email.py中的send函数
            # 注意：需要先配置send_email.py中的邮件参数
            # send()  # 取消注释并配置邮件参数后使用

            self.logger.info("邮件通知功能需要配置邮件参数后启用")
            print("提示: 邮件通知功能需要在send_email.py中配置邮件参数")

            return True

        except Exception as e:
            self.logger.error(f"发送邮件通知失败: {str(e)}")
            return False

    def cleanup_old_logs(self, keep_days=7):
        """清理旧的日志文件"""
        try:
            if not os.path.exists(self.log_dir):
                return

            current_time = datetime.now()

            for item in os.listdir(self.log_dir):
                item_path = os.path.join(self.log_dir, item)

                # 获取文件/目录的修改时间
                if os.path.isfile(item_path) or os.path.isdir(item_path):
                    file_time = datetime.fromtimestamp(os.path.getmtime(item_path))
                    days_old = (current_time - file_time).days

                    if days_old > keep_days:
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                            self.logger.info(f"删除旧日志文件: {item_path}")
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                            self.logger.info(f"删除旧日志目录: {item_path}")

        except Exception as e:
            self.logger.error(f"清理旧日志失败: {str(e)}")

    def run_full_inspection(self):
        """执行完整的巡检流程"""
        self.print_banner()

        # 设置日志
        log_file = self.setup_logging()
        self.inspection_results['start_time'] = datetime.now()

        self.logger.info("开始网络设备巡检流程")

        try:
            # 1. 连接测试
            print("\n步骤 1/5: 设备连接测试")
            if not self.connection_test():
                self.logger.error("连接测试失败，终止巡检流程")
                return False

            # 2. 设备信息采集
            print("\n步骤 2/5: 设备信息采集")
            if not self.device_inspection():
                self.logger.error("设备信息采集失败")

            # 3. 生成汇总报告
            print("\n步骤 3/5: 生成巡检报告")
            summary_report = self.generate_summary_report()

            # 4. 压缩结果
            print("\n步骤 4/5: 压缩巡检结果")
            zip_file = self.compress_results()

            # 5. 发送通知
            print("\n步骤 5/5: 发送邮件通知")
            self.send_notification(zip_file)

            # 清理旧日志
            self.cleanup_old_logs()

            self.inspection_results['end_time'] = datetime.now()
            total_time = self.inspection_results['end_time'] - self.inspection_results['start_time']

            print("\n" + "=" * 80)
            print("巡检流程完成!")
            print(f"总耗时: {total_time.total_seconds():.2f}秒")
            print(f"成功设备: {len(self.inspection_results['success_devices'])}")
            print(f"失败设备: {len(self.inspection_results['failed_devices'])}")
            if summary_report:
                print(f"汇总报告: {summary_report}")
            if zip_file:
                print(f"压缩文件: {zip_file}")
            print("=" * 80)

            self.logger.info("网络设备巡检流程完成")
            return True

        except Exception as e:
            self.logger.error(f"巡检流程异常: {str(e)}")
            return False

    def run_interactive_mode(self):
        """交互式模式"""
        self.print_banner()

        while True:
            print("\n请选择操作:")
            print("1. 设备连接测试")
            print("2. 设备信息采集")
            print("3. 完整巡检流程")
            print("4. 压缩现有结果")
            print("5. 清理旧日志")
            print("0. 退出程序")

            choice = input("\n请输入选择 (0-5): ").strip()

            if choice == '1':
                log_file = self.setup_logging()
                self.connection_test()
            elif choice == '2':
                log_file = self.setup_logging()
                self.device_inspection()
            elif choice == '3':
                self.run_full_inspection()
            elif choice == '4':
                zip_file = self.compress_results()
                if zip_file:
                    print(f"压缩完成: {zip_file}")
            elif choice == '5':
                days = input("请输入保留天数 (默认7天): ").strip()
                try:
                    days = int(days) if days else 7
                    self.cleanup_old_logs(days)
                    print("清理完成")
                except ValueError:
                    print("输入的天数无效，使用默认值7天")
                    self.cleanup_old_logs()
            elif choice == '0':
                print("程序退出")
                break
            else:
                print("无效选择，请重新输入")


def main():
    """主函数"""
    inspection = NetworkInspection()

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--auto':
            # 自动模式：执行完整巡检流程
            inspection.run_full_inspection()
        elif sys.argv[1] == '--test':
            # 测试模式：仅执行连接测试
            log_file = inspection.setup_logging()
            inspection.connection_test()
        elif sys.argv[1] == '--collect':
            # 采集模式：仅执行信息采集
            log_file = inspection.setup_logging()
            inspection.device_inspection()
        else:
            print("无效参数。支持的参数:")
            print("  --auto    : 执行完整巡检流程")
            print("  --test    : 仅执行连接测试")
            print("  --collect : 仅执行信息采集")
            print("  无参数    : 进入交互式模式")
    else:
        # 交互式模式
        inspection.run_interactive_mode()


if __name__ == '__main__':
    main()
