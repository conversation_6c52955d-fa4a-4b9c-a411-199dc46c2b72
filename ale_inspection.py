#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
ALE设备专用巡检程序
支持ALE设备的tech-support命令和TFTP文件传输
"""

import os
import sys
import ftplib
import time
from datetime import datetime
from netmiko import ConnectHandler
from openpyxl.reader.excel import load_workbook
from multiprocessing.pool import ThreadPool


class ALEInspection:
    """ALE设备巡检类"""
    
    def __init__(self):
        self.device_file = "template.xlsx"  # 使用现有的xlsx文件
        self.pool = ThreadPool(10)
        self.success = []
        self.fail = []
        self.log_dir = "LOG"
        self.logtime = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        
        # 创建LOG目录
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
            print(f"创建LOG目录: {self.log_dir}")
    
    def load_excel(self):
        """加载Excel文件"""
        try:
            wb = load_workbook(self.device_file)
            return wb
        except FileNotFoundError:
            print(f"Excel文件不存在: {self.device_file}")
            return None
    
    def get_device_info(self):
        """获取设备信息"""
        try:
            wb = self.load_excel()
            if not wb:
                return
                
            ws1 = wb[wb.sheetnames[0]]
            for row in ws1.iter_rows(min_row=2, max_col=9):
                if str(row[1].value).strip() == '#':
                    continue
                    
                info_dict = {
                    'ip': row[2].value,
                    'protocol': row[3].value,
                    'port': row[4].value,
                    'username': row[5].value,
                    'password': row[6].value,
                    'secret': row[7].value,
                    'device_type': row[8].value,
                    'cmd_list': self.get_cmd_info(wb[row[8].value.lower().strip()])
                }
                yield info_dict
                
        except Exception as e:
            print(f"读取设备信息错误: {e}")
    
    def get_cmd_info(self, cmd_sheet):
        """获取命令信息"""
        cmd_list = []
        try:
            for row in cmd_sheet.iter_rows(min_row=2, max_col=2):
                if str(row[0].value).strip() != "#" and row[1].value:
                    cmd_list.append(row[1].value.strip())
            return cmd_list
        except Exception as e:
            print(f"读取命令信息错误: {e}")
            return []
    
    def connect_device(self, host):
        """连接设备"""
        try:
            if host['protocol'].lower().strip() == 'ssh':
                host['port'] = host['port'] if (host['port'] not in [22, None]) else 22
            elif host['protocol'].lower().strip() == 'telnet':
                host['port'] = host['port'] if (host['port'] not in [23, None]) else 23
                host['device_type'] = host['device_type'] + '_telnet'
            
            # 移除不需要的参数
            connect_params = {
                'device_type': host['device_type'],
                'host': host['ip'],
                'username': host['username'],
                'password': host['password'],
                'port': host['port']
            }
            
            if host['secret']:
                connect_params['secret'] = host['secret']
            
            if 'huawei' in host['device_type']:
                connect_params['conn_timeout'] = 15
            
            connect = ConnectHandler(**connect_params)
            return connect
            
        except Exception as e:
            print(f"连接设备失败 {host['ip']}: {e}")
            self.fail.append(host['ip'])
            return None
    
    def execute_ale_tech_support(self, connection, device_ip):
        """执行ALE设备的tech-support命令并下载日志文件"""
        try:
            print(f"开始执行ALE设备 {device_ip} 的tech-support命令...")
            
            # 执行tech-support命令
            output = connection.send_command("show tech-support", delay_factor=2)
            print(f"tech-support命令执行完成: {device_ip}")
            
            # 等待文件生成
            time.sleep(5)
            
            # 需要的日志文件列表
            log_files = [
                "tech_support_layer3.log",
                "tech_support_layer2.log", 
                "tech_support.log"
            ]
            
            # 下载日志文件
            downloaded_files = []
            for log_file in log_files:
                if self.download_file_via_tftp(device_ip, log_file):
                    downloaded_files.append(log_file)
            
            if downloaded_files:
                print(f"成功下载 {device_ip} 的日志文件: {downloaded_files}")
                return True
            else:
                print(f"未能下载 {device_ip} 的任何日志文件")
                return False
                
        except Exception as e:
            print(f"执行tech-support失败 {device_ip}: {e}")
            return False
    
    def download_file_via_tftp(self, device_ip, filename):
        """通过TFTP下载文件"""
        try:
            # 这里需要根据实际情况配置TFTP服务器
            # 由于TFTP需要服务器配置，这里提供FTP的替代方案
            return self.download_file_via_ftp(device_ip, filename)
            
        except Exception as e:
            print(f"TFTP下载文件失败 {device_ip}/{filename}: {e}")
            return False
    
    def download_file_via_ftp(self, device_ip, filename):
        """通过FTP下载文件（TFTP的替代方案）"""
        try:
            # 这里需要根据实际的FTP服务器配置进行修改
            # 示例配置，需要根据实际环境调整
            ftp_server = device_ip  # 假设设备本身支持FTP
            ftp_user = "admin"      # 需要根据实际情况配置
            ftp_password = "password"  # 需要根据实际情况配置
            
            # 创建设备专用目录
            device_log_dir = os.path.join(self.log_dir, f"{device_ip}_{self.logtime}")
            if not os.path.exists(device_log_dir):
                os.makedirs(device_log_dir)
            
            # 本地文件名包含设备IP
            local_filename = f"{device_ip}_{filename}"
            local_path = os.path.join(device_log_dir, local_filename)
            
            # 尝试FTP下载
            with ftplib.FTP(ftp_server) as ftp:
                ftp.login(ftp_user, ftp_password)
                with open(local_path, 'wb') as local_file:
                    ftp.retrbinary(f'RETR {filename}', local_file.write)
            
            print(f"成功下载文件: {local_path}")
            return True
            
        except Exception as e:
            print(f"FTP下载文件失败 {device_ip}/{filename}: {e}")
            # 如果FTP失败，创建一个占位文件记录尝试
            device_log_dir = os.path.join(self.log_dir, f"{device_ip}_{self.logtime}")
            if not os.path.exists(device_log_dir):
                os.makedirs(device_log_dir)
            
            placeholder_file = os.path.join(device_log_dir, f"{device_ip}_{filename}.failed")
            with open(placeholder_file, 'w') as f:
                f.write(f"下载失败: {e}\n时间: {datetime.now()}\n")
            
            return False
    
    def execute_regular_commands(self, connection, device_ip, cmd_list):
        """执行常规巡检命令"""
        try:
            device_log_dir = os.path.join(self.log_dir, f"{device_ip}_{self.logtime}")
            if not os.path.exists(device_log_dir):
                os.makedirs(device_log_dir)
            
            for cmd in cmd_list:
                try:
                    output = connection.send_command(cmd)
                    
                    # 保存命令输出
                    cmd_filename = cmd.replace(' ', '_').replace('/', '_') + '.txt'
                    cmd_file_path = os.path.join(device_log_dir, f"{device_ip}_{cmd_filename}")
                    
                    with open(cmd_file_path, 'w', encoding='utf-8') as f:
                        f.write(f"命令: {cmd}\n")
                        f.write(f"设备: {device_ip}\n")
                        f.write(f"时间: {datetime.now()}\n")
                        f.write("=" * 50 + "\n")
                        f.write(output)
                    
                    print(f"命令执行完成: {device_ip} - {cmd}")
                    
                except Exception as e:
                    print(f"命令执行失败: {device_ip} - {cmd} - {e}")
            
            return True
            
        except Exception as e:
            print(f"执行常规命令失败 {device_ip}: {e}")
            return False
    
    def inspect_device(self, host):
        """巡检单个设备"""
        device_ip = host['ip']
        device_type = host['device_type'].lower()
        
        print(f"开始巡检设备: {device_ip} ({device_type})")
        
        # 连接设备
        connection = self.connect_device(host)
        if not connection:
            return
        
        try:
            # 如果是ALE设备，先执行tech-support
            if 'alcatel' in device_type or 'ale' in device_type:
                print(f"检测到ALE设备: {device_ip}")
                
                # 执行tech-support并下载日志
                tech_support_success = self.execute_ale_tech_support(connection, device_ip)
                
                if tech_support_success:
                    print(f"ALE设备 {device_ip} tech-support处理完成")
                else:
                    print(f"ALE设备 {device_ip} tech-support处理失败")
            
            # 执行常规巡检命令
            if host['cmd_list']:
                regular_success = self.execute_regular_commands(connection, device_ip, host['cmd_list'])
                if regular_success:
                    print(f"设备 {device_ip} 常规命令执行完成")
            
            # 记录成功
            self.success.append(device_ip)
            print(f"设备巡检完成: {device_ip}")
            
        except Exception as e:
            print(f"设备巡检失败: {device_ip} - {e}")
            self.fail.append(device_ip)
            
        finally:
            connection.disconnect()
    
    def run_inspection(self):
        """运行完整巡检流程"""
        print("=" * 60)
        print("开始网络设备巡检")
        print(f"时间: {self.logtime}")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # 获取设备列表并执行巡检
        devices = list(self.get_device_info())
        if not devices:
            print("没有找到设备配置")
            return
        
        print(f"发现 {len(devices)} 个设备")
        
        # 并发执行巡检
        for host in devices:
            self.pool.apply_async(self.inspect_device, args=(host,))
        
        self.pool.close()
        self.pool.join()
        
        end_time = datetime.now()
        
        # 打印结果
        print("\n" + "=" * 60)
        print("巡检完成")
        print("=" * 60)
        print(f"总设备数: {len(devices)}")
        print(f"成功设备: {len(self.success)}")
        print(f"失败设备: {len(self.fail)}")
        print(f"耗时: {(end_time - start_time).total_seconds():.2f}秒")
        
        if self.success:
            print("\n成功设备:")
            for device in self.success:
                print(f"  ✓ {device}")
        
        if self.fail:
            print("\n失败设备:")
            for device in self.fail:
                print(f"  ✗ {device}")
        
        # 压缩LOG文件夹
        self.compress_and_email()
    
    def compress_and_email(self):
        """压缩LOG文件夹并发送邮件"""
        try:
            from zip_file import compress_zip
            
            zip_filename = f"inspection_results_{self.logtime}.zip"
            
            if compress_zip(self.log_dir, zip_filename):
                print(f"\n✓ 巡检结果已压缩: {zip_filename}")
                
                # 发送邮件
                try:
                    from send_email import send
                    print("准备发送邮件...")
                    # send()  # 取消注释并配置邮件参数后使用
                    print("邮件发送功能需要在send_email.py中配置参数")
                    
                except Exception as e:
                    print(f"邮件发送失败: {e}")
            else:
                print("✗ 压缩失败")
                
        except Exception as e:
            print(f"压缩和邮件发送失败: {e}")


def main():
    """主函数"""
    print("ALE设备专用巡检程序")
    print("支持tech-support命令和日志文件下载")
    print("=" * 60)
    
    # 检查Excel文件
    if not os.path.exists("template.xlsx"):
        print("错误: template.xlsx文件不存在")
        print("请确保Excel配置文件存在")
        return
    
    # 创建巡检实例并运行
    inspector = ALEInspection()
    inspector.run_inspection()


if __name__ == '__main__':
    main()
