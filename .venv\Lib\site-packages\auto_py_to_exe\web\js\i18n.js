// Constants used in translation map
const applicationName = 'auto-py-to-exe';
const helpPostLinkAnchorTagContents =
  'href="https://nitratine.net/blog/post/issues-when-using-auto-py-to-exe/?utm_source=auto_py_to_exe&utm_medium=application_link&utm_campaign=auto_py_to_exe_help&utm_content=bottom" target="_blank"';
const onFileModeAdditionalFilesHelpAnchorTagContents =
  'href="https://stackoverflow.com/a/13790741/" target="_blank" style="text-decoration: none;"';

// Translation mappings
const translationMap = {
  ui: {
    // Static UI elements
    title: {
      scriptLocation: {
        en: 'Script Location',
        zh: '脚本位置',
        zh_tw: '腳本位置',
        uk: 'Місцезнаходження скрипта',
        ru: 'Расположение скрипта',
        be: 'Змесцаванне скрыпту',
        de: 'Scriptpfad',
        pt_br: 'Localização do Script',
        es: 'Localización del Script',
        es_419: 'Ubicacion del archivo',
        tr: 'Script Konumu',
        th: 'ตำแหน่งสคริปต์',
        fr: 'Emplacement des scripts',
        he: 'מיקום סקריפט',
        hi: 'स्क्रिप्ट स्थान',
        hu: 'Szkript helye',
        it: 'Locazione script',
        id: 'Lokasi script',
        cs: 'Umístění skriptu',
        el: 'Διαδρομή Αρχείου',
        ar: 'موقع البرنامج النصي',
        fa: 'محل فایل اسکریپت',
        sr: 'Lokacija skripte',
        sk: 'Lokácia skriptu',
        ja: 'スクリプトの場所',
        pl: 'Lokalizacja skryptu',
        fi: 'Skriptin sijainti',
        vi: 'Vị trí tập lệnh',
        nl: 'Script Locatie',
        ko: '스크립트 위치',
        bg: 'Местонахождение на скрипта',
      },
      language: {
        en: 'Language',
        zh: '语言',
        zh_tw: '語言',
        uk: 'Мова',
        ru: 'идиома',
        be: 'Мова',
        de: 'Sprache',
        pt_br: 'Idioma',
        es: 'Idioma',
        es_419: 'Idioma',
        tr: 'Deyim',
        th: 'สำนวน',
        fr: 'Langage',
        he: 'שפה',
        hi: 'भाषा',
        hu: 'Nyelv',
        it: 'Idioma',
        id: 'Idiom',
        cs: 'Idiom',
        el: 'ιδίωμα',
        ar: 'لغة. مثل',
        fa: 'زبان',
        sr: 'Jezik',
        sk: 'Slovenčina',
        ja: '言語',
        pl: 'Język',
        fi: 'Kieli',
        vi: 'Ngôn ngữ',
        nl: 'Taal',
        ko: '언어',
        bg: 'Език',
      },
      oneFile: {
        en: 'Onefile',
        zh: '单文件',
        zh_tw: '單檔案',
        uk: 'Один файл',
        ru: 'Один файл',
        be: 'Адзін файл',
        de: 'Eine Datei',
        pt_br: 'Arquivo Único',
        es: 'Archivo Único',
        es_419: 'Tipo de archivo',
        tr: 'Tek Dosya',
        th: 'หนึ่งไฟล์',
        fr: 'un fichier',
        he: 'קובץ-יחיד',
        hi: 'एक फ़ाइल',
        hu: 'Egy fájl',
        it: 'Un file',
        id: 'Satu Berkas',
        cs: 'Jeden soubor',
        el: 'Ένα αρχείο',
        ar: 'ملف',
        fa: 'فایل یکپارچه',
        sr: 'Jedana datoteka',
        sk: 'Jeden súbor',
        ja: '1つのファイル',
        pl: 'Wynik konwersji skryptu',
        fi: 'Yksi tiedosto',
        vi: 'Một tập tin',
        nl: 'Een Bestand',
        ko: '단일 파일 여부',
        bg: 'Един файл',
      },
      consoleWindow: {
        en: 'Console Window',
        zh: '控制台窗口',
        zh_tw: '控制台視窗',
        uk: 'Консольний застосунок',
        ru: 'Консольное приложение',
        be: 'Кансольны дадатак',
        de: 'Konsolen Anwendung',
        pt_br: 'Janela do Console',
        es: 'Consola de Windows',
        es_419: 'Tipo de consola',
        tr: 'Konsol Penceresi',
        th: 'หน้าต่างคอนโซล',
        fr: 'Console Windows',
        he: 'חלון מסוף',
        hi: 'कंसोल विंडो',
        hu: 'Konzol ablak',
        it: 'Finestra console',
        id: 'Jendela konsol',
        cs: 'Okno konzole',
        el: 'Εφαρμογή παραθύρου',
        ar: 'نافذة وحدة التحكم',
        fa: 'پنجره خط فرمان',
        sr: 'Konzolni prozor',
        sk: 'Okno konzole',
        ja: 'コンソール画面',
        pl: 'Wygląd aplikacji po konwersji .py',
        fi: 'Konsoli-ikkuna',
        vi: 'Cửa sổ bảng điều khiển',
        nl: 'Console Venster',
        ko: '콘솔 화면',
        bg: 'Конзолно приложение',
      },
      icon: {
        en: 'Icon',
        zh: '图标',
        zh_tw: '圖示',
        uk: 'Іконка',
        ru: 'Иконка',
        be: 'Іконка',
        de: 'Icon',
        pt_br: 'Ícone',
        es: 'Icono',
        es_419: 'Ícono',
        tr: 'Simge',
        th: 'ไอคอน',
        fr: 'Icone',
        he: 'אייקון',
        hi: 'आइकॉन',
        hu: 'Ikon',
        it: 'Icona',
        id: 'Ikon',
        cs: 'Ikona',
        el: 'Εικονίδιο',
        ar: 'أيقونة',
        fa: 'آیکون',
        sr: 'Ikona',
        sk: 'Ikona',
        ja: 'アイコン',
        pl: 'Dodanie ikony',
        fi: 'Kuvake',
        vi: 'Biểu tượng',
        nl: 'Icoon',
        ko: '아이콘',
        bg: 'Икона',
      },
      additionalFiles: {
        en: 'Additional Files',
        zh: '附加文件',
        zh_tw: '附加檔案',
        uk: 'Додаткові файли',
        ru: 'Дополнительные файлы',
        be: 'Дадатковыя файлы',
        de: 'Dateien hinzufügen',
        pt_br: 'Arquivos Adicionais',
        es: 'Archivos adicionales',
        es_419: 'Archivos adicionales',
        tr: 'Ek dosyalar',
        th: 'ไฟล์เพิ่มเติม',
        fr: 'Fichiers additionnels',
        he: 'קבצים נוספים',
        hi: 'अतिरिक्त फ़ाइलें',
        hu: 'További fájlok',
        it: 'File addizionali',
        id: 'File tambahan',
        cs: 'Další soubory',
        el: 'Πρόσθετα αρχεία',
        ar: 'ملفات إضافية',
        fa: 'فایل‌های تکمیلی',
        sr: 'Dodatne datoteke',
        sk: 'Dodatočné súbory',
        ja: '追加ファイル',
        pl: 'Dodanie dodatkowych plików',
        fi: 'Lisätiedostot',
        vi: 'Các tệp bổ sung',
        nl: 'Extra Bestanden',
        ko: '추가 파일',
        bg: 'Допълнителни Файлове',
      },
      advanced: {
        en: 'Advanced',
        zh: '高级',
        zh_tw: '進階',
        uk: 'Додаткові',
        ru: 'Расширенные',
        be: 'Дадатковыя',
        de: 'Erweitert',
        pt_br: 'Avançado',
        es: 'Avanzado',
        es_419: 'Avanzado',
        tr: 'Daha Fazla',
        th: 'ขั้นสูง',
        fr: 'Paramètres avancées',
        he: 'מתקדם',
        hi: 'उन्नत सेटिंग्स',
        hu: 'Haladó',
        it: 'Opzioni avanzate',
        id: 'Lanjutan',
        cs: 'Pokročilé',
        el: 'Για προχωρημένους',
        ar: 'متقدم',
        fa: 'پیشرفته',
        sr: 'Napredno',
        sk: 'Pokročilé',
        ja: '高度な設定',
        pl: 'Parametry zaawansowane',
        fi: 'Lisävaihtoehdot',
        vi: 'Trình độ cao',
        nl: 'Geanvanceerd',
        ko: '고급',
        bg: 'Разширения',
      },
      settings: {
        en: 'Settings',
        zh: '设置',
        zh_tw: '設定',
        uk: 'Налаштування',
        ru: 'Настройки',
        be: 'Налады',
        de: 'Einstellungen',
        pt_br: 'Configurações',
        es: 'Configuraciones',
        es_419: 'Configuración',
        tr: 'Ayarlar',
        th: 'การตั้งค่า',
        fr: 'Paramètres',
        he: 'הגדרות',
        hi: 'सेटिंग्स',
        hu: 'Beállítások',
        it: 'Impostazioni',
        id: 'Pengaturan',
        cs: 'Nastavení',
        el: 'Ρυθμίσεις',
        ar: 'إعدادات',
        fa: 'تنظیمات',
        sr: 'Postavke',
        sk: 'Nastavenia',
        ja: '設定',
        pl: 'Ustawienia',
        fi: 'Asetukset',
        vi: 'Cài đặt',
        nl: 'Instellingen',
        ko: '설정',
        bg: 'Настройки',
      },
      currentCommand: {
        en: 'Current Command',
        zh: '当前命令',
        zh_tw: '當前命令',
        uk: 'Поточна команда',
        ru: 'Текущая команда',
        be: 'Бягучая каманда',
        de: 'Aktueller Befehl',
        pt_br: 'Comando Atual',
        es: 'Comando Actual',
        es_419: 'Comando actual',
        tr: 'Geçerli Komut',
        th: 'คำสั่งปัจจุบัน',
        fr: 'Commande',
        he: 'פקודה נוכחית',
        hi: 'वर्तमान कमांड',
        hu: 'Aktuális parancs',
        it: 'Comando',
        id: 'Perintah saat ini',
        cs: 'Aktuální příkaz',
        el: 'Τρέχον Εντολή',
        ar: 'الأمر الحالي',
        fa: 'دستور فعلی',
        sr: 'Trenutna komanda',
        sk: 'Aktuálny príkaz',
        ja: '現在のコマンド',
        pl: 'Aktualnie stworzone polecenie konwersji',
        fi: 'Tämänhetkinen komento',
        vi: 'Lệnh hiện tại',
        nl: 'Huidig Comando',
        ko: '현재 명령',
        bg: 'Настояща команда',
      },
      output: {
        en: 'Output',
        zh: '输出',
        zh_tw: '輸出',
        uk: 'Вивід',
        ru: 'Вывод',
        be: 'Вывад',
        de: 'Ausgabe',
        pt_br: 'Saída',
        es: 'Salida',
        es_419: 'Salida',
        tr: 'Çıktı',
        fr: 'Sortie',
        he: 'פלט',
        hi: 'नतीजा',
        hu: 'Kimenet',
        it: 'Output',
        id: 'Keluaran',
        cs: 'Výstup',
        el: 'Έξοδος',
        ar: 'المخرجات',
        fa: 'خروجی',
        sr: 'Izlaz (Output)',
        sk: 'Výstup (Output)',
        ja: '出力',
        pl: 'Wyjście',
        fi: 'Ulostulo',
        vi: 'Lối ra',
        nl: 'Uitvoer',
        ko: '출력',
        bg: 'Изход',
      },
      specificOptions: {
        en: `${applicationName} Specific Options`,
        zh: `${applicationName} 特定的选项`,
        zh_tw: `${applicationName} 特定的選項`,
        uk: `Особливі параметри ${applicationName}`,
        ru: `Особые параметры ${applicationName}`,
        be: `Асаблівыя параметры ${applicationName}`,
        de: `${applicationName} spezifische Optionen`,
        pt_br: `Opções Específicas ${applicationName}`,
        es: `Opciones Específicas ${applicationName}`,
        es_419: `Opciones Específicas ${applicationName}`,
        tr: `${applicationName} ye Özel Seçenekler`,
        th: `${applicationName} ตัวเลือกเฉพาะ`,
        fr: 'Options spécifiques',
        he: `אפשרויות ייחודיות של ${applicationName}`,
        hi: `${applicationName} विशिष्ट विकल्प`,
        hu: `${applicationName} Különleges opciók`,
        it: `Opzioni specifiche ${applicationName}`,
        id: `Pilihan Spesifik ${applicationName}`,
        cs: `Přesné nastavení ${applicationName}`,
        el: `${applicationName} Συγκεκριμένες Επιλογές`,
        ar: `خيارات محددة ${applicationName}`,
        fa: `گزینه‌های ویژه ${applicationName}`,
        sr: 'Precizno podešavanje auto-py-to-exe-a',
        sk: `Špecifické nastavenia ${applicationName}`,
        ja: `${applicationName} 固有の設定`,
        pl: `${applicationName} Opcje szczególne`,
        fi: `${applicationName} Tarkat asetukset`,
        vi: `${applicationName} Cài đặt chính xác`,
        nl: `${applicationName} Specifieke Opties`,
        ko: `${applicationName} 상세 옵션`,
        bg: `${applicationName} Допълнителни Опции`,
      },
      outputDirectory: {
        en: 'Output Directory',
        zh: '输出路径',
        zh_tw: '輸出路徑',
        uk: 'Директорія Виводу',
        ru: 'Папка Вывода',
        be: 'Тэчка Вываду',
        de: 'Ausgabeverzeichnis',
        pt_br: 'Diretório de Saída',
        es: 'Directorio de Salida',
        es_419: 'Ubicacion de salida',
        tr: 'Çıktı Dizini',
        th: 'Output Directory',
        fr: 'Repertoire de sortie',
        he: 'ספרית הפלט',
        hi: 'आउटपुट निर्देशिका',
        hu: 'Kimenet helye',
        it: 'Cartella di output',
        id: 'Direktori Keluaran',
        cs: 'Složka výstupu',
        el: 'Διαδρομή Έξοδου Αρχείου',
        ar: 'دليل المخرجات',
        fa: 'پوشه خروجی',
        sr: 'Izlazni direktorijum',
        sk: 'Adresár/Priečinok výstupu',
        ja: '出力先ディレクトリ',
        pl: 'Katalog wyjściowy',
        fi: 'Ulostulon kansio',
        vi: 'Thư mục đầu ra',
        nl: 'Uitvoer Pad',
        ko: '출력 디렉토리',
        bg: 'Изходяща директория',
      },
      increaseRecursionLimit: {
        en: 'Increase Recursion Limit',
        zh: '增加递归限制',
        zh_tw: '增加遞歸限制',
        uk: 'Збільшити Ліміт Глибини Рекурсії',
        ru: 'Увеличить Лимит Рекурсии',
        be: 'Павялічыць Ліміт Рэкурсіі',
        de: 'Erhöhen der Rekursionsbegrenzung',
        pt_br: 'Limite de Recursividade',
        es: 'Límite de Recursividad',
        es_419: 'Límite de Recursividad',
        tr: 'Özyineleme Sınırını Artırın',
        th: 'ขีด จำกัด การเรียกซ้ำ',
        fr: 'Augmenter la limite de récursivité',
        he: 'הגדל את מגבלת הרקורסיה',
        hi: 'पुनरावृत्ति सीमा बढ़ाना',
        hu: 'Rekurzió limit növelése',
        it: 'Aumenta limite recursione',
        id: 'Tingkatkan Batas Pengulangan',
        cs: 'Zvýšit limit rekurze',
        el: 'Αύξηση ορίου αναδρομής',
        ar: 'زيادة حد التكرار',
        fa: 'افزایش محدودیت تکرار',
        sr: 'Povećaj granicu rekurzije',
        sk: 'Zvýšiť limit rekurzie',
        ja: '再帰回数の上限を増やす',
        pl: 'Zwiększenie limitu rekursji',
        fi: 'Nosta rekursioiden rajaa',
        vi: 'Tăng giới hạn đệ quy',
        nl: 'Verhoog Herhaling Limiet',
        ko: '재귀 한도 증가',
        bg: 'Увеличи лимит на рекурсията',
      },
      manuallyProvideOptions: {
        en: 'Manually Provide Options',
        zh: '手动提供选项',
        zh_tw: '手動提供選項',
        uk: 'Параметри Вказані Вручну',
        ru: 'Вручную Указанные Параметры',
        be: 'Параметры Пазначаныя Ручна',
        de: 'Optionen manuell eingeben',
        pt_br: 'Fornecer Opções Manualmente',
        es: 'Opciones Manuales',
        es_419: 'Opciones manuales',
        tr: 'Seçenekleri Manuel Olarak Sağlayın',
        th: 'ระบุตัวเลือกด้วยตนเอง',
        fr: 'Fournir manuellement des options',
        he: 'הגדרת אפשרויות באופן ידני',
        hi: 'हस्तक्षेप से विकल्प प्रदान करें',
        hu: 'Beállítások manuális hozzáadása',
        it: 'Opzioni manuali',
        id: 'Berikan Opsi secara manual',
        cs: 'Manuálně poskytnout nastavení',
        el: 'Χειροκίνητες Επιλογές',
        ar: 'قم بتوفير الخيارات يدويًا',
        fa: 'ارائه دستی تنظیمات',
        sr: 'Ručno unesi podešavanja',
        sk: 'Manuálne pridať nastavenia',
        ja: '手動設定',
        pl: 'Opcje manualne',
        fi: 'Säädä asetuksia manuaalisesti',
        vi: 'Cung cấp cài đặt theo cách thủ công',
        nl: 'Handmatig Toegestaande Opties',
        ko: '직접 넣을 옵션',
        bg: 'Ръчно добавени параметри',
      },
      manualArgumentInput: {
        en: 'Manual Argument Input',
        zh: '手动参数输入',
        zh_tw: '手動參數輸入',
        uk: 'Додати аргументи вручну',
        ru: 'Ручной Ввод Аргументов',
        be: 'Ручны Ўвод Аргументаў',
        de: 'Manuelle Argumenteingabe',
        pt_br: 'Entrada Manual de Argumentos',
        es: 'Argumentos de Entrada',
        es_419: 'Argumentos de entrada',
        tr: 'Manuel Argüman Girişi',
        th: 'ระบุตัวเลือกด้วยตนเอง',
        fr: 'Saisie manuelle des arguments',
        he: 'הזנת פרמטרים באופן ידני',
        hi: 'हस्तक्षेप तर्क इनपुट',
        hu: 'Manuális paraméterbevitel',
        it: 'Input opzioni manuali',
        id: 'Masukkan Argumen secara manual',
        cs: 'Manuální vstup argumentů',
        el: 'Χειροκίνητη προσθήκη argument',
        ar: 'إدخال المُعْطَيات يدوي',
        fa: 'ورود دستی آرگومان',
        sr: 'Ručni unos argumenata',
        sk: 'Manuálny vstup argumentov',
        ja: '手動指定引数',
        pl: 'Manualne wprowadzanie argumentów',
        fi: 'Manuaaliset argumentit',
        vi: 'Nhập thủ công các đối số',
        nl: 'Manuele Feit Ingave',
        ko: '직접 입력할 매개변수',
        bg: 'Ръчно добавяне на аргументи',
      },
      configuration: {
        en: 'Configuration',
        zh: '配置',
        zh_tw: '配置',
        uk: 'Конфігурація',
        ru: 'Конфигурация',
        be: 'Канфігурацыя',
        de: 'Konfiguration',
        pt_br: 'Configuração',
        es: 'Configuración',
        es_419: 'Configuración',
        tr: 'Yapılandırma',
        th: 'การกำหนดค่า',
        fr: 'Paramétrage',
        he: 'הגדרות',
        hi: 'विन्यास',
        hu: 'Konfiguráció',
        it: 'Configurazione',
        id: 'Konfigurasi',
        cs: 'Konfigurace',
        el: 'Διαμόρφωση',
        ar: 'ترتيب',
        fa: 'پیکربندی',
        sr: 'Konfiguracija',
        sk: 'Konfigurácia',
        ja: '構成',
        pl: 'Konfiguracja',
        fi: 'Konfiguraatio',
        vi: 'Cấu hình',
        nl: 'Configuratie',
        ko: '구성',
        bg: 'Конфигурация',
      },
    },
    button: {
      browse: {
        en: 'Browse',
        zh: '浏览',
        zh_tw: '瀏覽',
        uk: 'Місцезнаходження',
        ru: 'Расположение',
        be: 'Змесцаванне',
        de: 'Durchsuchen',
        pt_br: 'Explorar',
        es: 'Mostrar',
        es_419: 'Mostrar',
        tr: 'Araştır',
        th: 'เลือก',
        fr: 'Navigateur',
        he: 'חיפוש',
        hi: 'बटन',
        hu: 'Böngészés',
        it: 'Sfoglia',
        id: 'Telusuri',
        cs: 'Prohlížet',
        el: 'Αναζήτηση',
        ar: 'تصفح',
        fa: 'جستجو',
        sr: 'Pronađi',
        sk: 'Prehliadať',
        ja: '開く',
        pl: 'Przeglądaj',
        fi: 'Selaa',
        vi: 'Xem',
        nl: 'Zoeken',
        ko: '찾아보기',
        bg: 'Търсене',
      },
      oneDirectory: {
        en: 'One Directory',
        zh: '单目录',
        zh_tw: '單目錄',
        uk: 'Одна Директорія',
        ru: 'Одна Папка',
        be: 'Адна Тэчка',
        de: 'Ein Verzeichnis',
        pt_br: 'Um Diretório',
        es: 'Un directorio',
        es_419: 'Directorio unico',
        tr: 'Tek Dizin',
        th: 'หนึ่งไดเรกทอรี',
        fr: 'un répertoire',
        he: 'תיקיה-אחת',
        hi: 'एक निर्देशिका',
        hu: 'Egy könyvtár',
        it: 'Una cartella',
        id: 'Satu Direktori',
        cs: 'Jedna složka',
        el: 'Μια Διαδρομή',
        ar: 'دليل واحد',
        fa: 'یک فولدر',
        sr: 'Jedan direktorijum',
        sk: 'Jeden priečinok/adresár',
        ja: '1つのディレクトリ',
        pl: 'Jeden katalog',
        fi: 'Yksi kansio',
        vi: 'Một thư mục',
        nl: 'Een Map',
        ko: '단일 디렉토리',
        bg: 'Една директория',
      },
      oneFile: {
        en: 'One File',
        zh: '单文件',
        zh_tw: '單檔案',
        uk: 'Один файл',
        ru: 'Один файл',
        de: 'Eine Datei',
        pt_br: 'Arquivo Único',
        es: 'Un Archivo',
        es_419: 'Unico archivo',
        tr: 'Tek Dosya',
        th: 'ไฟล์เดียว',
        fr: 'Un fichier',
        he: 'קובץ-יחיד',
        hi: 'एक फ़ाइल',
        hu: 'Egy fájl',
        it: 'Un file',
        id: 'Satu Berkas',
        cs: 'Jeden soubor',
        el: 'Ένα Αρχείο',
        ar: 'ملف واحد',
        fa: 'یک فایل',
        sr: 'Jedana datoteka',
        sk: 'Jeden súbor',
        ja: '1つのファイル',
        pl: 'Jeden plik',
        fi: 'Yksi tiedosto',
        vi: 'Một tập tin',
        nl: 'Een Bestand',
        ko: '단일 파일',
        bg: 'Един файл',
      },
      consoleBased: {
        en: 'Console Based',
        zh: '基于控制台的',
        zh_tw: '基於控制台',
        uk: 'Консольний Застосунок',
        ru: 'Консольное Приложение',
        be: 'Кансольны Дадатак',
        de: 'Befehlszeilenbasiert',
        pt_br: 'Baseado em Console',
        es: 'Consola básica',
        es_419: 'Consola básica',
        tr: 'Konsol Tabanlı',
        th: 'คอนโซล',
        fr: 'Présence de la console',
        he: 'מבוסס מסוף',
        hi: 'कंसोल आधारित',
        hu: 'Konzol alapú',
        it: 'Mostra console',
        id: 'Berdasarkan Konsol',
        cs: 'V konzoli',
        el: 'Βασιμένο σε Παράθυρο Κονσόλας',
        ar: 'قائم على وحدة التحكم',
        fa: 'مبتنی بر کنسول',
        sr: 'Konzolna aplikacija',
        sk: 'V konzole',
        ja: 'コンソールベース',
        pl: 'Aplikacja w konsoli',
        fi: 'Konsolipohjainen',
        vi: 'Trong bảng điều khiển',
        nl: 'Console Gebaseerd',
        ko: '콘솔 기반',
        bg: 'Конзолно приложение',
      },
      windowBased: {
        en: 'Window Based (hide the console)',
        zh: '基于窗口的 (隐藏控制台)',
        zh_tw: '基於視窗 (隱藏控制台)',
        uk: 'Віконний Застосунок (приховати консоль)',
        ru: 'Оконное Приложение (скрыть консоль)',
        be: 'Аконны Дадатак (схаваць кансоль)',
        de: 'Fensterbasiert (Befehlszeile ausblenden)',
        pt_br: 'Baseado em Janelas (ocultar o console)',
        es: 'Consola de Windows (Ocultar consola)',
        es_419: 'Ventana (ocultar consola)',
        tr: 'Pencere Tabanlı (Konsolu gizleyin)',
        th: 'หน้าต่าง (ซ่อนคอนโซล)',
        fr: "Basé sur windows (la console n'est pas visible)",
        he: 'מבוסס חלון (הסתר את המסוף)',
        hi: 'विंडो आधारित',
        hu: 'Grafikus felhasználói felület (konzol eltűntetése)',
        it: 'Mostra finestra (nascondi la console)',
        id: 'Berdasarkan Jendela (sembunyikan konsol)',
        cs: 'V okně (skrýt konzoli)',
        el: 'Βασισμένο σε Παράθυρο (απόκρυψη κονσόλας)',
        ar: 'قائم على النافذة (إخفاء وحدة التحكم)',
        fa: 'مبتنی بر پنجره (عدم نمایش کنسول)',
        sr: 'Aplikacija sa prozorom (sakrij konzolu)',
        sk: 'Aplikáci v okne (skriť konzolu)',
        ja: 'ウィンドウベース (コンソールを非表示にする)',
        pl: 'Aplikacja okienkowa (ukrycie konsoli)',
        fi: 'Ikkunapohjainen (piilota konsoli)',
        vi: 'Trong cửa sổ (ẩn bàn điều khiển)',
        nl: 'Venster Gebaseerd',
        ko: '창 기반 (콘솔창 가리기)',
        bg: 'Приложение с прозорец (скриване на конзолата)',
      },
      addFiles: {
        en: 'Add Files',
        zh: '添加文件',
        zh_tw: '新增檔案',
        uk: 'Додати Файли',
        ru: 'Добавить Файлы',
        be: 'Дадаць Файлы',
        de: 'Dateien hinzufügen',
        pt_br: 'Adicionar Arquivos',
        es: 'Añadir archivos',
        es_419: 'Añadir archivos',
        tr: 'Dosyalar Ekle',
        th: 'เพิ่มไฟล์',
        fr: 'Ajout de fichiers',
        he: 'הוספת קבצים',
        hi: 'फ़ाइलें जोड़ें',
        hu: 'Fájlok hozzáadása',
        it: 'Aggiungi file',
        id: 'Tambahkan Berkas',
        cs: 'Přidat soubory',
        el: 'Προσθήκη Αρχείων',
        ar: 'إضافة ملفات',
        fa: 'افزودن فایل‌ها',
        sr: 'Dodaj datoteku',
        sk: 'Pridať súbory',
        ja: 'ファイルの追加',
        pl: 'Dodaj pliki',
        fi: 'Lisää tiedostoja',
        vi: 'Thêm các tập tin',
        nl: 'Bestanden Toevoegen',
        ko: '파일 추가',
        bg: 'Добавяне на файлове',
      },
      addFolder: {
        en: 'Add Folder',
        zh: '添加目录',
        zh_tw: '新增目錄',
        uk: 'Додати директорію',
        ru: 'Добавить Папку',
        be: 'Дадаць Тэчку',
        de: 'Verzeichnis hinzufügen',
        pt_br: 'Adicionar Pasta',
        es: 'Añadir carpeta',
        es_419: 'Añadir carpeta',
        tr: 'Klasör Ekle',
        th: 'เพิ่มแฟ้มใหม่',
        fr: 'Ajout de dossiers',
        he: 'הוספת תיקיה',
        hi: 'फ़ोल्डर जोड़ें',
        hu: 'Mappa hozzáadása',
        it: 'Aggiungi cartelle',
        id: 'Tambahkan Folder',
        cs: 'Přidat složku',
        el: 'Προσθήκη Φακέλου',
        ar: 'أضف مجلدًا',
        fa: 'افزودن پوشه',
        sr: 'Dodaj direktorijum',
        sk: 'Pridať priečinok/adresár',
        ja: 'フォルダの追加',
        pl: 'Dodaj katalog',
        fi: 'Lisää kansio',
        vi: 'Thêm thư mục',
        nl: 'Map Toevoegen',
        ko: '폴더 추가',
        bg: 'Добавяне на папки',
      },
      addBlank: {
        en: 'Add Blank',
        zh: '添加空白',
        zh_tw: '新增空白',
        uk: 'Додати шаблон',
        ru: 'Добавить шаблон',
        be: 'Дадаць шаблон',
        de: 'Leerzeichen hinzufügen',
        pt_br: 'Adicionar em Branco',
        es: 'Añadir en blanco',
        es_419: 'Añadir en blanco',
        tr: 'Boş Alan Ekle',
        th: 'เพิ่มช่องว่าง',
        fr: 'Ajout de pages',
        he: 'הוספת ריק',
        hi: 'रिक्त जोड़ें',
        hu: 'Üres hozzáadása',
        it: 'Aggiungi vuoto',
        id: 'Tambahkan Kosong',
        cs: 'Přidat prázdný',
        el: 'Προσθήκη Κενού Αρχείου',
        ar: 'أضف فارغًا',
        fa: 'افزودن فضای خالی',
        sr: 'Dodaj prazno',
        sk: 'Pridať prázdne',
        ja: '空白の追加',
        pl: 'Dodaj puste pole',
        fi: 'Lisää tyhjä',
        vi: 'Thêm trống',
        nl: 'Leeg Toevoegen',
        ko: '직접 입력',
        bg: 'Добавяне на шаблон',
      },
      importConfig: {
        en: 'Import Config From JSON File',
        zh: '从JSON文件导入配置',
        zh_tw: '從 JSON 檔案導入配置',
        uk: 'Імпортувати Конфігурацію з файлу JSON',
        ru: 'Импортировать Конфигурацию из JSON Файла',
        be: 'Імпартаваць Канфігурацыю з файла JSON',
        de: 'Konfiguration aus JSON-Datei importieren',
        pt_br: 'Importar Config de Arquivo JSON',
        es: 'Importar Configuración de Archivo JSON',
        es_419: 'Importar Configuración de un archivo JSON',
        tr: 'Yapılandırmayı JSON Dosyasından İçe Aktar',
        th: 'นำเข้าการตั้งค่า (ไฟล์ JSON)',
        fr: "Importation de la configuration d'un fichier JSON",
        he: 'ייבוא הגדרות מקובץ JSON',
        hi: 'आयात विन्यास',
        hu: 'Konfiguráció importálása JSON fájlból',
        it: 'Importa configurazione da file JSON',
        id: 'Impor Config dari Berkas JSON',
        cs: 'Importovat konfiguraci z JSON souboru',
        el: 'Εισαγωγή Ρυθμίσεων από JSON αρχείο',
        ar: 'استيراد التكوين من ملف JSON',
        fa: 'وارد کردن پیکربندی از فایل JSON',
        sr: 'Učitaj konfiguracuju iz JSON datoteke',
        sk: 'Importovať konfiguráciu s JSON súboru',
        ja: '構成をJSONファイルからインポート',
        pl: 'Import konfiguracji z pliku JSON',
        fi: 'Tuo konfiguraatio JSON-tiedostosta',
        vi: 'Nhập cấu hình từ tệp JSON',
        nl: 'Importeer Config Uit JSON Bestand',
        ko: 'JSON 파일에서 구성 가져오기',
        bg: 'Импортиране на конфигурация от JSON файл',
      },
      exportConfig: {
        en: 'Export Config To JSON File',
        zh: '将配置导出到JSON文件',
        zh_tw: '將配置導出到 JSON 檔案',
        uk: 'Експортувати Конфігурацію у файл JSON',
        ru: 'Экспортировать Конфигурацию в JSON Файл',
        be: 'Экспартаваць Канфігурацыю ў файл JSON',
        de: 'Konfiguration in JSON-Datei exportieren',
        pt_br: 'Exportar Config para Arquivo JSON',
        es: 'Exportar Configuración para Archivo JSON',
        es_419: 'Exportar Configuración a un archivo JSON',
        th: 'ส่งออกการตั้งค่า (ไฟล์ JSON)',
        fr: 'Exportation de la configuration vers un fichier JSON',
        he: 'ייצוא הגדרות לקובץ JSON',
        hi: 'निर्यात विन्यास',
        hu: 'Konfiguráció exportálása JSON fájlba',
        it: 'Esporta configurazione su un file JSON',
        id: 'Ekspor Config ke Berkas JSON',
        cs: 'Exportovat konfiguraci do JSON souboru',
        el: 'Εξαγωγή ρυθμίσεων σε αρχείο JSON',
        ar: 'تصدير التكوين إلى ملف JSON',
        fa: 'ذخیره تنظیمات در فایل JSON',
        sr: 'Sačuvaj konfiguracuju kao JSON datoteku',
        sk: 'Exportovať konfiguráciu do JSON súboru',
        ja: '構成をJSONファイルへエクスポート',
        pl: 'Eksport konfiguracji do pliku JSON',
        fi: 'Vie konfiguraatio JSON-tiedostoon',
        vi: 'Xuất cấu hình sang tệp JSON',
        nl: 'Exporteer Config',
        ko: 'JSON 파일에 구성 저장하기',
        bg: 'Експортиране на конфигурация към JSON файл',
      },
      convert: {
        en: 'Convert .py to .exe',
        zh: '将.PY转换为.EXE',
        zh_tw: '將.PY 轉換為 .EXE',
        uk: 'Конвертувати .py в .exe',
        ru: 'Конвертировать .py В .exe',
        be: 'Канвертаваць .py Ў .exe',
        de: 'Konvertiere .py in .exe',
        pt_br: 'Converter .py para .exe',
        es: 'Convertir .py a .exe',
        es_419: 'Convertir .py a .exe',
        tr: ".py'yi .exe'ye dönüştürün",
        th: 'เริ่มการแปลงไฟล์',
        fr: 'Convert. .py vers .exe',
        he: 'המר פייתון לתוכנה',
        hi: 'परिवर्तित करें',
        hu: '.py fájl, .exe fájlba konvertálása',
        it: 'Converti .py a .exe',
        id: 'Konversi .py ke .exe',
        cs: 'Převést .py na .exe',
        el: 'Μετατροπή .py σε .exe',
        ar: 'تحويل .py إلى. exe',
        fa: 'تبدیل .py به .exe',
        sr: 'Konvertuj .py u .exe',
        sk: 'Konvertovať .py do .exe',
        ja: '.pyを.exeに変換',
        pl: 'Konwertuj .py na .exe',
        fi: 'Muunna .py-tiedosto .exe-tiedostoksi',
        vi: 'Chuyển đổi .py sang .exe',
        nl: 'Converteer .py naar .exe',
        ko: '.py에서 .exe로 전환',
        bg: 'Конвертиране на .py към .exe',
      },
      openOutputFolder: {
        en: 'Open Output Folder',
        zh: '打开输出目录',
        zh_tw: '打開輸出目錄',
        uk: 'Відкрити Кінцеву Директорію',
        ru: 'Открыть Папку Вывода',
        be: 'Адкрыць Тэчку Вываду',
        de: 'Ausgabeverzeichnis öffnen',
        pt_br: 'Abrir Pasta de Saída',
        es: 'Abrir Carpeta de Destino',
        es_419: 'Abrir carpeta de destino',
        tr: 'Çıktı Klasörünü Aç',
        th: 'เปิดโฟลเดอร์ผลลัพธ์',
        fr: 'Ouvrir le dossier de sortie',
        he: 'פתח ספרית פלט',
        hi: 'आउटपुट फ़ोल्डर खोलें',
        hu: 'Kimeneti mappa',
        it: 'Apri cartella di destinazione',
        id: 'Buka Folder Keluaran',
        cs: 'Otevřít složku výstupu',
        el: 'Άνοιγμα φακέλου εξαγωγής αρχείου',
        ar: 'فتح مجلد المخرجات',
        fa: 'باز کردن پوشه خروجی',
        sr: 'Otvori izlazni direktorijum',
        sk: 'Otvoriť priečinok/adresár výstupu',
        ja: '出力フォルダを開く',
        pl: 'Otwórz katalog wyjściowy',
        fi: 'Avaa ulostulon kansio',
        vi: 'Mở thư mục đầu ra',
        nl: 'Open Uitvoer Map',
        ko: '출력 폴더 열기',
        bg: 'Отваряне на изходна папка',
      },
      enable: {
        en: 'Enable',
        zh: '开启',
        zh_tw: '開啟',
        uk: 'Увімкнути',
        ru: 'Включить',
        be: 'Уключыць',
        de: 'Aktivieren',
        pt_br: 'Habilitar',
        es: 'Habilitar',
        es_419: 'Habilitar',
        tr: 'Aktif',
        th: 'เปิดใช้งาน',
        fr: 'Autorisé',
        he: 'להפעיל',
        hi: 'सक्षम करें',
        hu: 'Engedélyez',
        it: 'Abilita',
        id: 'Aktifkan',
        cs: 'Povolit',
        el: 'Συμπερίληψη',
        ar: 'يُمكَِن',
        fa: 'فعالسازی',
        sr: 'Omogući',
        sk: 'Povoliť',
        ja: '有効',
        pl: 'Włączyć',
        fi: 'Aktivoi',
        vi: 'Cho phép',
        nl: 'Ingeschakeld',
        ko: '활성화',
        bg: 'Включи',
      },
    },
    links: {
      helpPost: {
        en: 'Help Post',
        zh: '帮助帖子',
        zh_tw: '幫助文章',
        uk: 'Довідка',
        ru: 'Справка',
        be: 'Даведка',
        de: 'Hilfe-Post',
        pt_br: 'Artigo de Ajuda',
        es: 'Ayuda',
        es_419: 'Ayuda',
        tr: 'Yardım',
        th: 'บทความช่วยเหลือ',
        fr: "Message d'aide",
        he: 'פוסט עזרה',
        hi: 'सहायता पोस्ट"',
        hu: 'Súgó',
        it: 'Aiuto',
        id: 'Post Bantuan',
        cs: 'Příspěvek pomoci',
        el: 'Βοήθεια',
        ar: 'مساعدة آخر',
        fa: 'پست راهنما',
        sr: 'Pomoć',
        sk: 'Príspevok pomoci',
        ja: 'ヘルプ記事',
        pl: 'Pomoc',
        fi: 'Apua (Englanniksi)',
        vi: 'Đăng trợ giúp',
        nl: 'Help',
        ko: '도움말',
        bg: 'Помощ',
      },
    },
    placeholders: {
      pathToFile: {
        en: 'Path to file',
        zh: '文件路径',
        zh_tw: '檔案路徑',
        uk: 'Шлях до файлу',
        ru: 'Путь к файлу',
        be: 'Шлях да файла',
        de: 'Pfad zur Datei',
        pt_br: 'Caminho para Arquivo',
        es: 'Ruta de archivo',
        es_419: 'Ruta de archivo',
        tr: 'Dosya yolu',
        th: 'เส้นทางไปยังไฟล์',
        fr: 'Chemin vers le fichier',
        he: 'נתיב לקובץ',
        hi: 'फ़ाइल का पथ',
        hu: 'Fájl elérési útvonala',
        it: 'Percorso file',
        id: 'Jalur ke berkas',
        cs: 'Cesta k souboru',
        el: 'Διαδρομή στο αρχείο',
        ar: 'مسار الملف',
        fa: 'مسیر فایل',
        sr: 'Putanja datoteke',
        sk: 'Cesta k súboru',
        ja: 'ファイルのパス',
        pl: 'Ścieżka pliku',
        fi: 'Polku tiedostoon',
        vi: 'Đường dẫn tập tin',
        nl: 'Bestand Pad',
        ko: '파일 경로',
        bg: 'Път към файла',
      },
      icoFile: {
        en: '.ico file',
        zh: '图标路径',
        zh_tw: '圖示路徑',
        uk: '.ico файл',
        ru: '.ico файл',
        be: '.ico файл',
        de: '.ico Datei',
        pt_br: 'Arquivo .ico',
        es: 'Archivo .ico',
        es_419: 'Archivo .ico',
        tr: '.ico Dosyasi',
        th: '.ico ไฟล์',
        fr: '.ico fichier',
        he: 'קובץ .ico',
        hi: '.ico फ़ाइल',
        hu: '.ico fájl',
        it: '.ico file',
        id: '.ico berkas',
        cs: '.ico soubor',
        el: 'Αρχείο .ico',
        ar: 'ملف .ico',
        fa: 'فایل .ico',
        sr: '.ico datoteka',
        sk: '.ico súbor',
        ja: '.ico ファイル',
        pl: 'plik .ico',
        fi: '.ico tiedosto',
        vi: 'tập tin .ico',
        nl: '.ico Bestand',
        ko: '.ico 파일',
        bg: '.ico файл',
      },
      directory: {
        en: 'DIRECTORY',
        zh: '目录',
        zh_tw: '目錄',
        uk: 'Директорія',
        ru: 'Директория',
        be: 'Дырэкторыя',
        de: 'VERZEICHNIS',
        pt_br: 'DIRETÓRIO',
        es: 'Directorio',
        es_419: 'Directorio',
        tr: 'Dizin',
        th: 'ไดเรกทอรี',
        fr: 'REPERTOIRE',
        he: 'ספריה',
        hi: 'निर्देशिका',
        hu: 'KÖNYVTÁR',
        it: 'CARTELLA',
        id: 'DIREKTORI',
        cs: 'SLOŽKA',
        el: 'Ευρετήριο',
        ar: 'الدليل',
        fa: 'فهرست راهنما',
        sr: 'Direktorijum',
        sk: 'PRIEČINOK',
        ja: 'ディレクトリ',
        pl: 'KATALOG',
        fi: 'KANSIO',
        vi: 'THÀNH PHẦN',
        nl: 'MAP',
        ko: '디렉토리',
        bg: 'Директория',
      },
      arguments: {
        en: 'ARGUMENTS',
        zh: '参数',
        zh_tw: '參數',
        uk: 'Аргументи',
        ru: 'Аргументы',
        be: 'Аргументы',
        de: 'ARGUMENTE',
        pt_br: 'ARGUMENTOS',
        es: 'ARGUMENTOS',
        es_419: 'ARGUMENTOS',
        tr: 'ARGÜMANLAR',
        th: 'อาร์กิวเมนต์',
        fr: 'ARGUMENTS',
        he: 'פרמטרים',
        hi: 'आर्ग्यूमेंट',
        hu: 'PARAMÉTEREK',
        it: 'OPZIONI',
        id: 'ARGUMEN',
        cs: 'ARGUMENTY',
        el: 'Παράμετροι',
        ar: 'المعطيات',
        fa: 'پارامترها',
        sr: 'Argumenti',
        sk: 'ARGUMENTY',
        ja: '引数',
        pl: 'ARGUMENTY',
        fi: 'ARGUMENTIT',
        vi: 'TRANH LUẬN',
        nl: 'ARGUMENTEN',
        ko: '매개변수',
        bg: 'Аргументи',
      },
    },
    helpText: {
      outputDirectory: {
        en: "The directory to put the output in. Will be created if it doesn't exist",
        zh: '用于放置输出的目录。如果不存在，将创建该目录',
        zh_tw: '用於放置輸出的目錄如果不存在，將自動創建該目錄',
        uk: 'Директорія, в яку буде зібрано кінцевий застосунок. Буде створено за необхідності',
        ru: 'Папка, в которую переместиться итоговое приложение. Будет создано при необходимости.',
        be: 'Тэчка, у якую перасунецца выніковы дадатак. Будзе створана пры неабходнасці.',
        de: 'Ausgabeverzeichnis. Wird erzeugt wenn es nicht existiert',
        pt_br: 'O diretório para colocar a saída. Será criado se não existir',
        es: 'El directorio para colocar el archivo de salida. Será creado si no existe',
        es_419: 'El directorio para colocar el archivo de salida. Será creado si no existe',
        tr: 'Çıktının yerleştirileceği dizin. Mevcut değilse oluşturulacaktır.',
        th: 'Directory สำหรับ Output ไฟล์ จะถูกสร้างขึ้น ถ้า Directory นั้นไม่มี',
        fr: "Le répertoire sera créé s'il n'existe pas.",
        he: 'הספרייה להכניס את הפלט. תיווצר אם היא לא קיימת',
        hi: 'आउटपुट को रखने के लिए निर्देशिका। यह निर्मित किया जाएगा अगर यह मौजूद नहीं है।',
        hu: 'A kimenetet tartalmazó könyvtár. Ha nem létezik, létre lesz hozva',
        it: "Cartella dove mettere l'output, sarà creata se non esiste.",
        id: 'Direktori untuk menyimpan output. Akan dibuat jika tidak ada',
        cs: 'Složka kde bude výstup. Bude vytvořena pokud neexistuje.',
        el: 'Εάν δεν υπάρχει ο φάκελος εξόδου, θα δημιουργηθεί.',
        ar: 'الدليل المراد وضع الإخراج فيه. سيتم إنشاؤه إذا لم يكن موجودًا',
        fa: 'مسیر پوشه خروجی. در صورت وجود نداشتن، ساخته خواهد شد',
        sr: 'Direktorijum za izlaz. Napraviće se ako ne postoji.',
        sk: 'Priečinok výstupu. Ak neexistuje, bude vytvorený',
        ja: '出力したものを置くディレクトリ。存在しない場合は作成されます。',
        pl: 'Katalog, w którym należy umieścić dane wyjściowe. Zostanie utworzony, jeśli nie istnieje.',
        fi: 'Kansio johon valmis .exe-tiedosto laitetaan. Kansio luodaan, jos se ei ole jo olemassa.',
        vi: 'Thư mục chứa đầu ra. Nó sẽ được tạo ra nếu nó không tồn tại.',
        nl: 'De uitvoermap. Wordt aangemaakt als de map niet bestaat.',
        ko: '결과물이 저장될 디렉토리. 디렉토리가 존재하지 않으면 자동 생성됩니다.',
        bg: 'Изходната папка ще бъде създадена, ако не съществува.',
      },
      increaseRecursionLimit: {
        en: 'Having this enabled will set the recursion limit to 5000 using sys.setrecursionlimit(5000).',
        zh: '启用此功能将使用sys.setrecursionlimit（5000）将递归限制设置为5000。',
        zh_tw: '啟用此功能將使用 sys.setrecursionlimit（5000）將遞歸限制設置為5000。',
        uk: 'Якщо увімкнено, встановить ліміт глибини рекурсії рівний 5000 використовуючи sys.setrecursionlimit(5000).',
        ru: 'Если включено установит лимит рекурсии равный 5000 с помощью sys.setrecursionlimit(5000).',
        be: 'Калі ўключана ўсталяваць ліміт рэкурсіі роўны 5000 з дапамогаю sys.setrecursionlimit(5000).',
        de: 'Setzt das Rekursionslimit auf 5000 per sys.setrecursionlimit(5000).',
        pt_br: 'Ativar isso definirá o limite de recursão para 5000 usando sys.setrecursionlimit(5000)',
        es: 'Al activar esto se definirá el límite de recursión a 5000 usando sys.setrecursionlimit(5000)',
        es_419: 'Al activar esto se definirá el límite de recursión a 5000 usando sys.setrecursionlimit(5000)',
        tr: "Bunun etkinleştirilmesi, sys.setrecursionlimit(5000) kullanılarak yineleme sınırını 5000'e ayarlayacaktır.",
        th: 'การเปิดใช้งานนี้จะตั้งค่าขีดจำกัดการเรียกซ้ำเป็น 5000 โดยใช้ sys.setrecursionlimit(5000)',
        fr: "L'activation de cette option définira la limite de récursivité à 5000 en utilisant sys.setrecursionlimit(5000).",
        he: 'הפעלת אפשרות זו תגדיר את מגבלת הרקורסיה ל-5000 בשימוש sys.setrecursionlimit(5000)',
        hi: 'इसे सक्षम करने से यह स्विचित किया जाएगा कि रिकर्शन सीमा 5000 हो, sys.setrecursionlimit(5000) का उपयोग करके।',
        hu: 'Ha ez engedélyezve van, a rekurzió limit 5000-re lesz állítva a sys.setrecursionlimit(5000) segítségével.',
        it: 'Abilitando questa opzione imposterà il limite di recursione a 5000 utilizzando sys.setrecursionlimit(5000).',
        id: 'Dengan mengaktifkan ini, pengaturan limit rekursi akan diatur ke 5000 menggunakan sys.setrecursionlimit(5000)',
        cs: 'Pokud zapnuté, limit rekurze se zvýší ná 5000 pomocí sys.setrecursionlimit(5000).',
        el: 'Έχοντας ενεργοποιήσει το όριο που θα χρησιμοποιηθεί είναι 5000 με την παράμετρο sys.setrecursionlimit(5000).',
        ar: 'سيؤدي تمكين هذا إلى تعيين حد العودية على 5000 باستخدام sys.setrecursionlimit (5000).',
        fa: 'با فعال کردن این گزینه، محدودیت بازگشت به 5000 با استفاده از sys.setrecursionlimit(5000) تنظیم می شود.',
        sr: 'Ako je omogućeno, ograničenje rekurzije će biti povećano na 5000 korišćenjem sys.setrecursionlimit(5000).',
        sk: 'Ak zapnuté, limit rekurzie sa zvýši na 5000 pomocou sys.setrecursiomlimit(5000).',
        ja: 'これを有効にすると、sys.setrecursionlimit(5000)を使い、再帰回数の上限を5000にします。',
        pl: 'Przy włączeniu tego ustawimy limit rekurencji na 5000 używając sys.setrecursionlimit(5000).',
        fi: 'Tämän kytkeminen päälle asettaa 5000 rekursiorajaksi käyttämällä sys.setrecursionlimit(5000).',
        vi: 'Nếu được bật, giới hạn đệ quy được tăng lên 5000 bằng cách sử dụng sys.setrecursionlimit(5000).',
        nl: 'Indien ingeschakeld, dan is de recurentie limiet beperkt tot 5000 gebruik makend van sys.setrecursionlimit(5000).',
        ko: '활성화 시 sys.setrecursionlimit(5000)을 사용해 재귀 한도를 5000까지 늘립니다.',
        bg: 'Във включено положение, лимитът на рекурсията ще бъде увеличен до 5000 използвайки sys.setrecursionlimit(5000).',
      },
      manualArgumentInput: {
        en: 'Inject raw text into the generated command.',
        zh: '将原始文本插入到生成的命令中。',
        zh_tw: '將原始文字插入到產生的命令中。',
        uk: 'Додасть текст в кінцеву команду',
        ru: 'Вставит текст в итоговую команду',
        be: 'Дадасць тэкст у выніковую каманду',
        de: 'Text zum generierten Befehl hinzufügen.',
        pt_br: 'Injete texto bruto no comando gerado.',
        es: 'Inserte texto bruto en el comando generado',
        es_419: 'Inserte texto bruto en el comando generado',
        tr: 'Oluşturulan komuta ham metin ekleyin.',
        th: 'ใส่ raw text ลงในคำสั่งที่สร้างขึ้น',
        fr: 'Injectez du texte brut dans la commande générée.',
        he: 'הכנס טקסט גולמי לפקודה שנוצרה',
        hi: 'उत्पन्न कमांड में कच्चा पाठ डालें।',
        hu: 'Saját szöveg hozzáadása a generált parancshoz',
        it: 'Inserisci testo forzatamente alla fine del comando generato.',
        id: 'Masukkan teks mentah ke dalam command yang dibuat',
        cs: 'Vložit text do vygenerovaného příkazu.',
        el: 'Εισάγετε το κείμενο για την εντολή',
        ar: 'أدخل نصًا خامًا في الأمر الذي تم إنشاؤه.',
        fa: 'افزودن متن خام به دستور تولید شده.',
        sr: 'Ubaci tekst u generisanu komandu.',
        sk: 'Manuálne pridať text do generovaného príkazu.',
        ja: '生成されたコマンドにテキストを挿入する。',
        pl: 'Wstrzyknij raw tekst do wygenerowanego polecenia.',
        fi: 'Lisää raakaa tekstiä generoituun komentoon',
        vi: 'Dán văn bản vào lệnh đã tạo.',
        nl: 'Voeg rauwe tekst in de gegenereerde commando',
        ko: '생성된 명령을 입력된 텍스트와 함께 실행합니다',
        bg: 'Добавяне на текст към генерираната команда',
      },
    },
    notes: {
      invalidIcoFormatWarning: {
        en: 'Warning: this file is not a valid .ico file',
        zh: '警告：此文件不是有效的 .ico 文件',
        zh_tw: '警告：此文件不是有效的 .ico 文件',
        uk: 'Попередження: це недопустимий файл .ico',
        ru: 'Предупреждение: этот файл не является допустимым файлом .ico',
        be: 'Папярэджанне: гэты файл не з’яўляецца дапушчальным файлам .ico',
        de: 'Warnung: Diese Datei ist keine gültige .ico-Datei',
        pt_br: 'Aviso: este arquivo não é um arquivo .ico válido',
        es: 'Advertencia: este archivo no es un archivo .ico válido',
        es_419: 'Advertencia: este archivo no es un archivo .ico válido',
        tr: 'Uyarı: Bu dosya geçerli bir .ico dosyası değil',
        th: 'คำเตือน: ไฟล์นี้ไม่ใช่ไฟล์ .ico ที่ถูกต้อง',
        fr: "Attention: ce fichier n'est pas un fichier .ico valide",
        he: 'אזהרה: קובץ זה אינו קובץ .ico חוקי',
        hi: 'चेतावनी: यह फ़ाइल मान्य .ico फ़ाइल नहीं है।',
        hu: 'Figyelmeztetés: ez nem egy érvényes .ico fájl!',
        it: 'Attenzione: questo file non è un file .ico valido',
        id: 'Peringatan: file ini bukan file .ico yang valid',
        cs: 'Upozornění: Tento soubor není platným souborem .ico',
        el: 'Προειδοποίηση: αυτό το αρχείο δεν είναι έγκυρο αρχείο .ico',
        ar: 'تحذير: هذا الملف ليس ملف .ico صالح',
        fa: 'هشدار: این فایل یک .ico معتبر نیست.',
        sr: 'Упозорење: ова датотека није важећа .ицо датотека',
        sk: 'Upozornenie: súbor nie je platný .ico súbor.',
        ja: '警告: このファイルは有効な .ico ファイルではありません',
        pl: 'Ostrzeżenie: ten plik nie jest prawidłowym plikiem .ico',
        fi: 'Varoitus: tämä tiedosto ei ole kelpaava .ico-tiedosto',
        vi: 'Cảnh báo: tệp này không phải là tệp .ico hợp lệ',
        nl: 'Waarschuwing: het bestand is geen geldig .ico bestand',
        ko: '경고: 이 파일은 유효한 .ico 파일이 아닙니다',
        bg: 'Внимание: това не е валиден .ico файл',
      },
      oneFileAdditionalFilesNote: {
        en:
          'Be careful when using additional files with onefile mode;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>read this</a>\n` +
          'and update your code to work with PyInstaller.',
        zh:
          '使用单文件模式的附加文件时要小心;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>read this</a>\n` +
          '并更新您的代码以使用PyInstaller。',
        zh_tw:
          '使用單檔案模式的附加檔案時要小心;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>read this</a>\n` +
          '並更新您的代碼以使用 PyInstaller。',
        uk:
          'Будьте уважні під час використання додаткових файлів в режимі onefile;' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>Прочитайте це</a>\n` +
          'і оновіть свій код для подальшого використання PyInstaller.',
        ru:
          'Будьте внимательны при использовании дополнительных файлов в режиме одного файла;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>Прочитайте Это</a>\n` +
          'и обновите свой код для работы с PyInstaller.',
        be:
          'Будзьце ўважлівымі пры выкананні дадатковых файлаў у рэжыме аднаго файла;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>Прачытайце Гэта</a>\n` +
          'і абнавіце свой код для работы з PyInstaller.',
        de:
          'Vorsicht bei Verwendung von zusätzlichen Dateien im Modus "Eine Datei";\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>Siehe</a>\n` +
          'und aktualisiere den Code zur Verwendung mit PyInstaller.',
        pt_br:
          'Tenha cuidado ao usar arquivos adicionais com o modo de arquivo único;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>leia isto</a>\n` +
          'e atualize seu código para trabalhar com PyInstaller.',
        es:
          'Tenga cuidado al utilizar archivos adicionales con el modo de un solo archivo;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>read this</a>\n` +
          'and update your code to work with PyInstaller.',
        es_419:
          'Tenga cuidado al utilizar archivos adicionales con el modo de un solo archivo;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>read this</a>\n` +
          'y actualiza tu codigo para funcionar con PyInstaller.',
        tr:
          'Tek dosya modunda ek dosyalar kullanırken dikkatli olun;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>read this</a>\n` +
          've kodunuzu PyInstaller ile çalışacak şekilde güncelleyin.',
        th:
          'โปรดใช้ความระมัดระวัง เมื่อใช้ไฟล์เพิ่มเติมกับโหมด onefile;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>read this</a>\n` +
          'และอัปเดต code ของคุณเพื่อทำงานกับ PyInstaller',
        fr:
          'Soyez prudent lorsque vous utilisez des fichiers supplémentaires avec le mode onefile;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>read this</a>\n` +
          "et mettez à jour votre code pour qu'il fonctionne avec PyInstaller.",
        he:
          'היזהר בעת שימוש בקבצים נוספים במצב קובץ-יחיד;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>קרא את זה</a>\n` +
          'ועדכן את הקוד שלך לעבודה עם PyInstaller.',
        hi:
          'Onefile मोड के साथ अतिरिक्त फ़ाइलों का उपयोग करते समय सावधान रहें।;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>read this</a>\n` +
          'और अपने कोड को PyInstaller के साथ काम करने के लिए अपडेट करें।',
        hu:
          'Légy óvatos, ha további fájlokat használ egyfájlos módban;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>olvasd el ezt</a>\n` +
          'és frissítsd a kódod, hogy működjön pyinstallerrel.',
        it:
          'Fai attenzione quando usi più di un file in modalità un file;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>leggi qui (articolo in inglese)</a>\n` +
          'e aggiorna il tuo codice per funzionare con PyInstaller.',
        id:
          'Berhati-hati saat menggunakan berkas tambahan dengan mode onefile;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>baca ini</a>\n` +
          'dan perbarui kode Anda untuk bekerja dengan PyInstaller.',
        cs:
          'Buďte opatrní když přidáváte soubory s režimem jednoho souboru;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>přečtěte si tohle (stránka v angličtině)</a>\n` +
          'a aktualizujte kód tak aby fungoval s PyInstaller.',
        el:
          'Να είσατε προσεκτικοί με την χρήση επιλογής ενός αρχείου\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>read this</a>\n` +
          'and update your code to work with PyInstaller.',
        ar:
          'كن حذرًا عند استخدام ملفات إضافية مع وضع الملف الواحد ؛ \n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>اقرا هذا</a>\n` +
          'وقم بتحديث التعليمات البرمجية الخاصة بك للعمل مع PyInstaller.',
        fa:
          'هنگام استفاده از فایل های اضافی با حالت onefile مراقب باشید؛\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>لینک را بخوانید</a>\n` +
          'و کد خود را برای کار با PyInstaller به روز کنید.',
        sr:
          'Budi oprezan kada koristiš dodatne datoteke u režimu jedne datoteke;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>pročitaj ovo (na engleskom)</a>\n` +
          'i ažuriraj svoj kod da bi radio sa PyInstaller.',
        sk:
          'Buďte opatrný pri pridávaní súborov v režime jedného súboru;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>prečítajte si tento príspevok (stránka v angličtine)</a>\n` +
          'a aktualizujte kód tak, aby fungoval s PyInstaller-om.',
        ja:
          '1つのファイルモードで追加ファイルを使用する際は、注意が必要です。;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>こちら (原文記事)</a>\n` +
          'で、PyInstallerで動作するようにコードを変更してください。',
        pl:
          'Należy zachować ostrożność przy używaniu dodatkowych plików w trybie jednoplikowym;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>Czytaj to</a>\n` +
          'i zaktualizuj swój kod, aby działał z PyInstaller.',
        fi:
          'Ole varovainen kun käytät lisätiedostoja yhden tiedoston tilassa;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>Lue tämä (englanniksi)</a>\n` +
          'ja päivitä koodisi toimimaan PyInstallerin kanssa.',
        vi:
          'Hãy cẩn thận khi thêm tệp với chế độ tệp đơn;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>přečtěte si tohle (stránka v angličtině)</a>\n` +
          'và cập nhật mã để hoạt động với PyInstaller.',
        nl:
          'Wees voorzichtig bij gebruik van extra bestanden in Eenbestan modus;\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}Lees dit</a>\n` +
          'en update je code voor het werken met PyInstaller.',
        ko:
          '단일 파일 모드와 추가 파일 모드를 같이 사용할 때 주의해야 합니다.\n' +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>이 문서</a>\n` +
          '를 읽고 PyInstaller 모듈을 사용해 코드를 수정하세요.',
        bg:
          "Внимавайте при използването на допълнителни файлове в режим 'един файл'\n" +
          `<a ${onFileModeAdditionalFilesHelpAnchorTagContents}>Прочетете това</a>\n` +
          'и проверете вашият код за съвместимост с PyInstaller.',
      },
      rootDirectory: {
        en: 'If you want to put files in the root directory, put a period (.) in the destination.',
        zh: '如果要将文件放到根目录中，请在目标目录中输入句点(.)。',
        zh_tw: '如果要將檔案放到根目錄中，請在目標目錄中輸入句點(.)。',
        uk: 'Якщо хочете розмістити файли в основній директорії, додайте крапку (.) на початку шляху.',
        ru: 'Если вы хотите расположить файлы в главной директории, вставьте точку (.) в начале пути.',
        be: 'Калі вы хочаце змесцаваць файл у галоўнай дырэкторыі, дадайце кропку (.) на пачатку шляху.',
        pt_br: 'Se você quiser colocar arquivos no diretório raiz, coloque um ponto (.) no destino.',
        de: 'Um Dateien im Rootverzeichnis zu speichern, verwende einen Punkt (.) bei der Angabe des Zielverzeichnisses.',
        es: 'Si quiere poner los archivos en el directorio raíz, ponga un punto (.) en el destino.',
        es_419: 'Si quiere poner los archivos en el directorio raíz, ponga un punto (.) en el destino.',
        tr: 'Dosyaları kök dizine koymak istiyorsanız, hedefe nokta (.) koyun.',
        th: 'หากคุณต้องการใส่ไฟล์ในไดเร็กทอรีราก ให้ใส่จุด (.) ที่ปลายทาง',
        fr: 'Si vous souhaitez placer des fichiers dans le répertoire racine, mettez un point (.) dans la destination.',
        he: 'אם אתה רוצה לשים קבצים בספריית השורש, שים נקודה (.) ביעד',
        hi: 'यदि आप फ़ाइलों को मूल निर्देशिका में डालना चाहते हैं, तो गंतव्य में एक बिंदु (.) डालें।',
        hu: 'Ha a fő könyvtárba szeretnéd elhelyezni a fájlokat, akkor rakj egy pontot (.) a célhoz.',
        it: 'Se vuoi mettere file nella cartella radice, inserisci (.) nella destinazione',
        id: 'Jika Anda ingin menyimpan berkas di direktori root, tambahkan titik (.) di tujuan.',
        cs: 'Pokud chcete přidat soubory do kořenové složky, přidejte tečku (.) do destinace.',
        el: 'Έαν θέλετε να εισάγετε τα αρχεία σας στον κεντρικό φάκελο απλά τοποθετήστε μια τελεία (.) στον προορισμό',
        ar: 'إذا كنت تريد وضع الملفات في الدليل الجذر ، فضع نقطة (.) في الوجهة.',
        fa: 'اگر می خواهید فایل ها را در پوشه اصلی قرار دهید، یک نقطه (.) در پوشه مقصد قرار دهید.',
        sr: 'Ako želiš da dodaš datoteke u osnovni direktorijum (tzv. root), dodaj tačku (.) u odredištu.',
        sk: 'Ak chcete přidať súbory do koreňového adresára, pridajte bodku (.) do destinácie.',
        ja: 'ルートディレクトリにファイルを置きたい場合は、移動先にピリオド (.) を入れます。',
        pl: 'Jeśli chcesz umieścić pliki w katalogu głównym, postaw kropkę (.) w miejscu przeznaczenia.',
        fi: 'Jos haluat laittaa tiedostoja juurikansioon, laita piste (.) kohteeseen.',
        vi: 'Nếu bạn muốn thêm tệp vào thư mục gốc, hãy thêm dấu chấm (.) vào đích.',
        nl: 'Als je de bestanden wil toevoegen aan de root map, gebruik een punt (.) in de bestemming',
        ko: '최상위 디렉토리에 파일을 넣으려고 한다면, 경로에 마침표 (.)를 입력하세요.',
        bg: 'Ако желаете да поставите файлове в главната папка сложете (.) в началото на пътя.',
      },
      somethingWrongWithOutput: {
        en:
          'Something wrong with your exe? Read ' +
          `<a ${helpPostLinkAnchorTagContents}>this post on how to fix common issues</a>` +
          ' for possible solutions.',
        zh:
          '你的exe有问题? 阅读 ' +
          `<a ${helpPostLinkAnchorTagContents}>这篇文章是关于如何修复常见问题的` +
          ' 寻找可能的解决方案。',
        zh_tw:
          '您的 exe 有問題? 請閱讀 ' +
          `<a ${helpPostLinkAnchorTagContents}>這篇文章是關於如何修復常見的問題</a>` +
          ' 尋找可能的解決方案。',
        uk:
          'Щось не так з вашим EXE? Можете з легкістю знайти відповідь' +
          `<a ${helpPostLinkAnchorTagContents}>тут, ознайомившись з описом більшості проблем</a>`,
        ru:
          'Что-то не так с вашим exe? Прочитайте ' +
          `<a ${helpPostLinkAnchorTagContents}>эту справку, с описанием большинства проблем</a>` +
          ' чтобы решить свою.',
        be:
          'Нешта не так з вашым exe? Прачытайце ' +
          `<a ${helpPostLinkAnchorTagContents}>гэтую даведку, дзе апісана бальшыня праблем</a>` +
          ' каб вырашыць сваю.',
        de:
          'Probleme mit der .exe-Datei? Lies ' +
          `<a ${helpPostLinkAnchorTagContents}>diesen Artikel zur Behebung von bekannten Problemen</a>` +
          ' für mögliche Lösungen.',
        pt_br:
          'Algo errado com seu exe? Leia ' +
          `<a ${helpPostLinkAnchorTagContents}>este artigo sobre como corrigir problemas comuns</a>` +
          ' para possíveis soluções.',
        es:
          'Algo fue mal con tu exe? Lea ' +
          `<a ${helpPostLinkAnchorTagContents}>este post contiene posibles problemas comunes</a>` +
          ' y posibles soluciones.',
        es_419:
          'Algo fue mal con tu archivo .exe? Lea ' +
          `<a ${helpPostLinkAnchorTagContents}>este post contiene posibles soluciones a problemas comunes</a>.`,
        tr:
          "exe'nizde bir sorun mu var? Oku " +
          `<a ${helpPostLinkAnchorTagContents}>yaygın sorunların nasıl çözüleceğiyle ilgili bu gönderiye bakin</a>` +
          ' olası çözümler için.',
        th:
          'มีอะไรผิดปกติกับ exe ของคุณ? อ่าน ' +
          `<a ${helpPostLinkAnchorTagContents}>โพสต์เกี่ยวกับวิธีแก้ไขปัญหาทั่วไป</a>` +
          ' สำหรับวิธีแก้ปัญหาที่เป็นไปได้',
        fr:
          'Quelque chose ne va pas avec votre exe? Lire ' +
          `<a ${helpPostLinkAnchorTagContents}>ce post sur la façon de résoudre les problèmes courants</a>` +
          ' pour les solutions possibles.',
        he:
          'משהו לא טוב עם התוכנה שנוצרה? קרא ' +
          `<a ${helpPostLinkAnchorTagContents}>את הפוסט הזה</a>` +
          ' לאיתור ותיקון תקלות נפוצות.',
        hi:
          'आपके एक्सी में कुछ गड़बड़ है? पढ़ें' +
          `<a ${helpPostLinkAnchorTagContents}>आम समस्याओं को कैसे ठीक करें, इस पोस्ट को पढ़ें।</a>` +
          'संभावित समाधानों के लिए।',
        hu:
          'Valami nem jó az exe fájloddal? Olvasd el ' +
          `<a ${helpPostLinkAnchorTagContents}>ezt a posztot a leggyakoribb hibák elhárításáról</a>` +
          ' lehetséges megoldásokért.',
        it:
          'Qualcosa è andato storto con il tuo exe? leggi ' +
          `<a ${helpPostLinkAnchorTagContents}>questo articolo su come risolvere problemi comuni (articolo in inglese)</a>` +
          ' per possibili soluzioni.',
        id:
          'Ada yang salah dengan exe Anda? Baca ' +
          `<a ${helpPostLinkAnchorTagContents}>artikel ini untuk menyelesaikan masalah-masalah umum</a>` +
          ' yang mungkin.',
        cs:
          'Něco špatně s exe souborem? Přečtěte si ' +
          `<a ${helpPostLinkAnchorTagContents}>tento příspěvek na jak opravit běžné chyby</a>` +
          ' pro možné řešení.',
        el:
          'Πήγε κάτι στραβά με το εκτελέσιμο (exe); Διαβάστε ' +
          `<a ${helpPostLinkAnchorTagContents}>this post on how to fix common issues</a>` +
          ' για πιθανές λύσεις.',
        ar:
          'شيء خاطئ مع exe الخاص بك؟ اقرأ ' +
          `<a ${helpPostLinkAnchorTagContents}>هذا المنشور حول كيفية إصلاح المشكلات الشائعة</a>` +
          ' للحلول الممكنة.',
        fa:
          'مشکلی در exe شما وجود دارد؟ بخوانید' +
          `<a ${helpPostLinkAnchorTagContents}>این پست درباره رفع مشکلات رایج است.</a>` +
          ' برای راه حل‌های ممکن.',
        sr:
          'Nešto nije u redu sa tvojim exe-om? Pročitaj ' +
          `<a ${helpPostLinkAnchorTagContents}>ovaj post o tome kako da popraviš česte greške,</a>` +
          ' za moguća rešenja problema.',
        sk:
          'Niečo nefunguje s vaším exe súborom? Přečtěte si ' +
          `<a ${helpPostLinkAnchorTagContents}>tento príspevok ako opraviť bežné chyby</a>` +
          ' pre možné riešenia.',
        ja:
          'exeに不具合がありますか？ ' +
          `<a ${helpPostLinkAnchorTagContents}>よくある問題を解決する方法についての記事</a>` +
          ' をご覧ください。',
        pl:
          'Coś nie tak z twoim exe? Przeczytaj ' +
          `<a ${helpPostLinkAnchorTagContents}>ten post o tym, jak naprawić typowe problemy</a>` +
          ' w celu uzyskania możliwych rozwiązań ',
        fi:
          'Tuliko vastaan ongelmia= lue' +
          `<a ${helpPostLinkAnchorTagContents}>tämä postaus (englanniksi) niin näet yleiset ongelmat</a>` +
          ' ja mahdolliset ratkaisut.',
        vi:
          'Đã xảy ra sự cố với tệp exe? Đọc ' +
          `<a ${helpPostLinkAnchorTagContents}>bài đăng này về cách khắc phục các lỗi phổ biến</a>` +
          ' cho một giải pháp có thể.',
        nl:
          'Is er iets mis met je .exe bestand?Lees ' +
          `<a ${helpPostLinkAnchorTagContents}>dit artikel hoe men algemene problemen oplost</a>` +
          ' voor mogelijke oplossingen.',
        ko:
          'exe 파일에 문제가 있나요? ' +
          `<a ${helpPostLinkAnchorTagContents}>자주 발생하는 오류의 수정 방법을 다룬 문서</a>` +
          '를 읽어서 문제를 해결해보세요',
        bg:
          'Има проблем с вашият .exe файл? Прочетете ' +
          `<a ${helpPostLinkAnchorTagContents}>този пост за справка с най-често срещаните проблеми</a>` +
          ' и как да ги разрешите.',
      },
    },
  },
  // Elements dynamically added to the DOM
  dynamic: {
    title: {
      // For the usage of constants.js:advancedSections
      generalOptions: {
        en: 'General Options',
        zh: '常规选项',
        zh_tw: '一般選項',
        uk: 'Основні Налаштування',
        ru: 'Основные Настройки',
        be: 'Асноўныя Налады',
        de: 'Allgemeine Optionen',
        pt_br: 'Opções Gerais',
        es: 'Opciones generales',
        es_419: 'Opciones generales',
        tr: 'Genel Seçenekler',
        th: 'ตัวเลือกทั่วไป',
        fr: 'Options Générales',
        he: 'אפשרויות כלליות',
        hi: 'सामान्य विकल्प',
        hu: 'Általános lehetőségek',
        it: 'Opzioni generali',
        id: 'Pilihan Umum',
        cs: 'Obyčejné nastavení',
        el: 'Γενικές Παράμετροι',
        ar: 'خيارات عامة',
        fa: 'گزینه‌های عمومی',
        sr: 'Osnovna podešavanja',
        sk: 'Obyčajné nastavenia',
        ja: '一般設定',
        pl: 'Opcje generalne',
        fi: 'Yleiset asetukset',
        vi: 'Cài đặt đơn giản',
        nl: 'Algemene Opties',
        ko: '일반 옵션',
        bg: 'Основни настройки',
      },
      whatToBundleWhereToSearch: {
        en: 'What to bundle, where to search',
        zh: '捆绑什么，搜索哪里',
        zh_tw: '怎樣捆綁，哪裡搜尋',
        uk: 'Що використовується і де це шукати',
        ru: 'Что используется и где это искать',
        be: 'Што выкарыстоўваецца і дзе гэта шукаць',
        de: 'Was zusammenfassen, wo suchen',
        pt_br: 'O que agrupar, onde buscar',
        es: 'Qué agrupar, dónde buscar',
        es_419: 'Qué agrupar, dónde buscar',
        tr: 'Ne paketlenir, nerede aranır',
        th: 'What to bundle, ค้นหาที่ไหน',
        fr: 'Quoi regrouper, où chercher',
        he: 'מה לאגד, איפה לחפש',
        hi: 'क्या बंडल करना है, कहाँ खोजना है',
        hu: 'Mit kell csomagolni, hol kell keresni',
        it: 'Cosa ragruppare, dove cercare',
        id: 'Apa yang dibundling, dimana cari',
        cs: 'Co přibalit, kde hledat',
        el: 'Που θέλετε να γίνει η αναζήση;',
        ar: 'ماذا تحزم ، أين تبحث',
        fa: 'موارد بسته‌بندی و مکان‌های جستجو',
        sr: 'Šta uključiti, gde ga naći',
        sk: 'Čo pribaliť, kde hladať',
        ja: '何を同梱するか、どこを探すか',
        pl: 'Co połączyć, gdzie szukać',
        fi: 'Mitä paketoida, mistä etsiä',
        vi: 'Gói gì, tìm ở đâu',
        nl: 'Wat samenbrengen, waar te  zoeken',
        ko: '번들 묶기 옵션 및 검색 옵션',
        bg: 'Какво да бъде включено, къде да бъде намерено',
      },
      howToGenerate: {
        en: 'How to generate',
        zh: '如何生成',
        zh_tw: '如何產生',
        uk: 'Налаштування генератора',
        ru: 'Настройки генератора',
        be: 'Налады генератара',
        de: 'Wie generieren',
        pt_br: 'Como gerar',
        es: 'Como generar',
        es_419: 'Como generar',
        tr: 'Nasıl oluşturulur',
        th: 'วิธีการ generate',
        fr: 'Comment générer',
        he: 'איך ליצור',
        hi: 'कैसे उत्पन्न करें',
        hu: 'Hogyan generáljunk',
        it: 'Come generare',
        id: 'Bagaimana cara generate',
        cs: 'Jak vygenerovat',
        el: 'Πως να γίνει η αναπαραγωή',
        ar: 'كيف تنشئ',
        fa: 'نحوه ی ساخت',
        sr: 'Kako generisati',
        sk: 'Ako generovať',
        ja: 'どのように生成するか',
        pl: 'Jak wygenerować',
        fi: 'Miten generoida',
        vi: 'Làm thế nào để tạo ra',
        nl: 'Hoe te genereren',
        ko: '생성 옵션',
        bg: 'Настройка на генератора',
      },
      windowsAndMacOsXSpecificOptions: {
        en: 'Windows And macOS X Specific Options',
        zh_tw: 'Windows 和 macOS X 特定選項',
        uk: 'Налаштування для Windows та macOS X',
        ru: 'Настройки для Windows и macOS X',
        be: 'Налады для Windows і macOS X',
        de: 'Windows- und macOS-spezifische Optionen',
        pt_br: 'Opções específicas para Windows e macOS X',
        es: 'Opciones específicas Windows y macOS X',
        es_419: 'Opciones específicas de Windows y de macOS X',
        tr: "Windows ve macOS X'e Özel Seçenekler",
        th: 'ตัวเลือกเฉพาะของ Windows และ macOS X',
        fr: 'Windows et macOS X options specifiques',
        he: 'אפשרויות מיוחדות למק וחלונות',
        hi: 'Windows और macOS X विशिष्ट विकल्प',
        hu: 'Windows és macOs X specifikus opciók',
        it: 'Opzioni specifice Windows e macOS X',
        id: 'Pilihan Windows dan macOS X',
        cs: 'Windows a macOS X specifické nastavení',
        el: 'Συγκεκριμένες Επιλογές για Windows και macOS X',
        ar: 'خيارات محددة لنظام التشغيل Windows و macOS X',
        fa: 'گزینه های خاص برای Windows و macOS X',
        sr: 'Specifična podešavanja za Windows i macOS X',
        sk: 'Nastavenia špecifické fre Windows a macOS X',
        ja: 'WindowsとmacOS X 固有の設定',
        pl: 'Opcje specyficzne dla systemów Windows i macOS X',
        fi: 'Yksilölliset asetukset Windowsille ja macOSille',
        vi: 'Cài đặt cụ thể của Windows và macOS X',
        nl: 'Windows en macOS X Specifieke Opties',
        ko: 'Windows와 macOS X 상세 옵션',
        bg: 'Настройки за macOS X и Windows',
      },
      windowsSpecificOptions: {
        en: 'Windows specific options',
        zh: 'Windows特定选项',
        zh_tw: 'Windows 特定選項',
        uk: 'Налаштування для Windows',
        ru: 'Настройки для Windows',
        be: 'Налады для Windows',
        de: 'Windows-spezifische Optionen',
        pt_br: 'Opções específicas para Windows',
        es: 'Opciones específicas Windows',
        es_419: 'Opciones específicas de Windows',
        tr: "Windows'a özel seçenekler",
        th: 'ตัวเลือกเฉพาะของ Windows',
        fr: 'Options spécifiques à Windows',
        he: 'אפשרויות מיוחדות לסביבת חלונות',
        hi: 'विंडोज़ विशिष्ट विकल्प',
        it: 'Opzioni specifice Windows',
        hu: 'Windows specifikus opciók',
        id: 'Pilihan Windows',
        cs: 'Windows specifické nastavení',
        el: 'Συγκεκριμένες Επιλογές για Windows',
        ar: 'خيارات Windows المحددة',
        fa: 'گزینه های خاص Windows',
        sr: 'Specifična podešavanja za Windows',
        sk: 'Nastavenia špecifické pre Windows',
        ja: 'Windows固有の設定',
        pl: 'Opcje dla systemu Windows',
        fi: 'Yksilälliset asetukset Windowsille',
        vi: 'Cài đặt cụ thể của Windows',
        nl: 'Windows Specifieke Opties',
        ko: 'Windows 전용 상세 옵션',
        bg: 'Настройки за Windows',
      },
      windowsSideBySideAssemblySearchingOptions: {
        en: 'Windows Side-by-side Assembly searching options (advanced)',
        zh: '窗口并排汇编搜索选项(高级)',
        zh_tw: '視窗並排彙編搜尋選項(進階)',
        uk: 'Параметри пошуку паралельних збірок Windows (додатково)',
        ru: 'Параметры поиска параллельных сборок Windows (дополнительно)',
        be: 'Параметры пошуку паралельных зборак Windows (дадаткова)',
        de: 'Windows Side-by-side Suchoptionen (Erweitert)',
        pt_br: 'Opções pesquisa do Windows Side-by-side Assembly (avançado)',
        es: 'Opciones de búsqueda de ensamblaje en paralelo para Windows (avanzadas)',
        es_419: 'Opciones de búsqueda de ensamblaje en paralelo para Windows (avanzadas)',
        tr: 'Windows Yan Yana Montaj arama seçenekleri (gelişmiş)',
        th: 'ตัวเลือกการค้นหา Assembly ของ Windows Side-by-side (ขั้นสูง)',
        fr: 'Options de recherche Windows Side-by-side Assembly (avancées)',
        he: 'אפשרויות חיפוש חלון לצד חלון (מתקדם)',
        hi: 'विंडोज़ साइड-बाई-साइड असेंबली खोज विकल्प (उन्नत)',
        hu: 'Windows Side-by-side Assembly keresés beállítások (fejlett)',
        it: 'Opzioni ricerca Windows Side-by-side Assembly (avanzato)',
        id: 'Pilihan pencarian Assembly Windows Side-by-side (lanjutan)',
        cs: 'Windows Side-by-side Assembly nastavení hledání (pokročilé)',
        el: 'Εργαλεία αναζήτησης για Windows με Assembly (για προχωρημένους)',
        ar: 'خيارات البحث عن تجميع Windows جنبًا إلى جنب (متقدمة)',
        fa: 'گزینه های جستجوی گروهی ویندوز کنار هم (پیشرفته)',
        sr: 'Windows Side-by-side Assembly podešavanja pretrage (napredno)',
        sk: 'Windows Side-by-side Assembly nastavenia hľadania (pokročilé)',
        ja: 'Windows Side-by-side アセンブリ検索オプション (上級者向け)',
        pl: 'Windows Side-by-side Assembly opcje przeszukiwania(zaawansowane)',
        fi: 'Windows Side-by-side Assembly etsimisasetukset (edistynyt)',
        vi: 'Windows Side-by-side Cài đặt tìm kiếm hội (nâng cao)',
        nl: 'Windows Zijde-bij-Zijde Samenvoeging Zoek Opties (geadvanceerd)',
        ko: 'Windows 병렬 어셈블리 검색 옵션 (고급)',
        bg: 'Windows Side-by-side Assembly (разширени)',
      },
      macOsxSpecificOptions: {
        en: 'macOS X specific options',
        zh: 'macOS X特定选项',
        zh_tw: 'macOS X 特定選項',
        uk: 'Налаштування для macOS X',
        ru: 'Настройки для macOS X',
        be: 'Налады для macOS X',
        de: 'macOS-spezifische Optionen',
        pt_br: 'Opções específicas para macOS X',
        es: 'Opciones específicas macOS X',
        es_419: 'Opciones específicas de macOS X',
        tr: "macOS X'e özel seçenekler",
        th: 'ตัวเลือกเฉพาะของ macOS X',
        fr: 'Options spécifiques pour macOS X',
        he: 'אפשרויות מיוחדות למק',
        hi: 'macOS X विशिष्ट विकल्प',
        hu: 'macOS X specifikus beállítások',
        it: 'Opzioni specifiche macOS X',
        id: 'Pilihan macOS X',
        cs: 'macOS X specifické nastavení',
        el: 'Συγκεκριμένες Επιλογές για  macOS X',
        ar: 'خيارات محددة لنظام التشغيل macOS X',
        fa: 'گزینه های خاص MacOS X',
        sr: 'Specifična podešavanja za macOS X',
        sk: 'Nastavenia špecifické pre macOS',
        ja: 'macOS X 固有の設定',
        pl: 'macOS X opcje specyficzne',
        fi: 'Yksilölliset asetukset macOSille',
        vi: 'Cài đặt cụ thể của macOS X',
        nl: 'macOS X Specifieke Opties',
        ko: 'macOS X 전용 옵션',
        bg: 'Специфични настройки за macOS X',
      },
      rarelyUsedSpecialOptions: {
        en: 'Rarely used special options',
        zh: '很少使用的特殊选项',
        zh_tw: '很少使用的特殊選項',
        uk: 'Додаткові опції, що рідко використовуються',
        ru: 'Редко используемые дополнительные опции',
        be: 'Дадатковыя опцыі, якія рэдка выкарыстоўваюцца',
        de: 'Selten genutzte Spezialoptionen',
        pt_br: 'Opções especiais raramente utilizadas',
        es: 'Opciones especiales raramente usadas',
        es_419: 'Opciones especiales raramente usadas',
        tr: 'Nadiren kullanılan özel seçenekler',
        th: 'ตัวเลือกพิเศษ (ไม่ค่อยได้ใช้)',
        fr: 'Options spéciales rarement utilisées',
        he: 'אפשרויות מיוחדות בשימוש נדיר',
        hi: 'कम प्रयोग किए जाने वाले विशेष विकल्प',
        hu: 'Ritkán használt speciális opciók',
        it: 'Opzioni speciali raramente utilizzate',
        id: 'Pilihan khusus yang jarang digunakan',
        cs: 'Málo používané speciální nastavení',
        el: 'Σπάνιες χρησιμοποιημένες επιλογές',
        ar: 'نادرًا ما يتم استخدام خيارات خاصة',
        fa: 'گزینه های خاص کمتر استفاده شده',
        sr: 'Retko korišćena podešavanja',
        sk: 'Málo používané špecifické nastavenia',
        ja: 'あまり使われない設定',
        pl: 'Rzadko wykorzystywane opcje specjalne',
        fi: 'Harvoin käytetyt erikoisasetukset',
        vi: 'Các cài đặt đặc biệt hiếm khi được sử dụng',
        nl: 'Uitzonderlijk gebruikte opties',
        ko: '사용이 드문 특수 옵션',
        bg: 'Рядко използвани допълнителни настройки',
      },
      other: {
        en: 'Other',
        zh: '其他',
        zh_tw: '其它',
        uk: 'Інше',
        ru: 'Другое',
        be: 'Іншае',
        de: 'Andere',
        pt_br: 'Outras',
        es: 'Otros',
        es_419: 'Otros',
        tr: 'Diğer',
        th: 'อื่นๆ',
        fr: 'Autres',
        he: 'אחר',
        hi: 'अन्य',
        hu: 'Egyéb',
        it: 'Altro',
        id: 'Lainnya',
        cs: 'Ostatní',
        el: 'Διάφορα',
        ar: 'آخر',
        fa: 'غیره',
        sr: 'Ostalo',
        sk: 'Iné',
        ja: 'その他',
        pl: 'Inne',
        fi: 'Muu',
        vi: 'Khác',
        nl: 'Andere',
        ko: '기타',
        bg: 'Други',
      },
    },
    button: {
      // For the usage of interface.js_createSubSectionInAdvanced
      browseForFile: {
        en: 'Browse for File',
        zh: '浏览文件',
        zh_tw: '瀏覽檔案',
        uk: 'Пошук файлу',
        ru: 'Поиск файла',
        be: 'Пошук файла',
        de: 'Datei suchen',
        pt_br: 'Procurar arquivo',
        es: 'Buscar archivo',
        es_419: 'Buscar archivo',
        tr: 'Dosyaya Gözat',
        th: 'เรียกดูไฟล์',
        fr: 'Rechercher un fichier',
        he: 'בחר קובץ',
        hi: 'फ़ाइल के लिए ब्राउज़ करें',
        hu: 'Fájl böngészése',
        it: 'Sfoglia file',
        id: 'Telusuri berkas',
        cs: 'Hledat soubor',
        el: 'Αναζήτηση Αρχείου',
        ar: 'تصفح بحثًا عن ملف',
        fa: 'انتخاب فایل',
        sr: 'Pronađi datoteku',
        sk: 'Hľadať súbor',
        ja: 'ファイルを参照',
        pl: 'Przeglądaj w celu znalezienia pliku',
        fi: 'Selaa tiedostoja',
        vi: 'Tìm kiếm tập tin',
        nl: 'Zoeken naar Bestand',
        ko: '파일 탐색',
        bg: 'Търсене на файл',
      },
      browseForFolder: {
        en: 'Browse for Folder',
        zh: '浏览文件夹',
        zh_tw: '瀏覽檔案夾',
        uk: 'Пошук директорії',
        ru: 'Поиск Папки',
        be: 'Пошук Тэчкі',
        de: 'Verzeichnis suchen',
        pt_br: 'Procurar Pasta',
        es: 'Buscar carpeta',
        es_419: 'Buscar carpeta',
        tr: 'Klasöre Gözat',
        th: 'เรียกดูโฟลเดอร์',
        fr: 'Rechercher un dossier',
        he: 'בחר תיקיה',
        hi: 'फ़ोल्डर के लिए ब्राउज़ करें',
        hu: 'Mappa böngeszése',
        it: 'Sfoglia archivio',
        id: 'Telusuri folder',
        cs: 'Hledat složku',
        el: 'Αναζητήση Φακέλου',
        ar: 'قم بالاستعراض بحثًا عن مجلد',
        fa: 'انتخاب پوشه',
        sr: 'Pronađi direktorijum',
        sk: 'Hľadať priečinok',
        ja: 'フォルダを参照',
        pl: 'Przeglądaj w celu znalezienia katalogu',
        fi: 'Selaa kansioita',
        vi: 'Thư mục tìm kiếm',
        nl: 'Zoeken naar Map',
        ko: '폴더 탐색',
        bg: 'Търсене на папка',
      },
      enable: {
        en: 'Enable',
        zh: '开启',
        zh_tw: '開啟',
        uk: 'Увімкнути',
        ru: 'Включить',
        be: 'Уключыць',
        de: 'Aktivieren',
        pt_br: 'Habilitar',
        es: 'Habilitar',
        es_419: 'Habilitar',
        tr: 'Aktif',
        th: 'เปิดใช้งาน',
        fr: 'Activer',
        he: 'להפעיל',
        hi: 'सक्षम करें',
        hu: 'Engedélyez',
        it: 'Abilita',
        id: 'Aktifkan',
        cs: 'Zapnout',
        el: 'Ενεργοποιήση',
        ar: 'يُمكَِن',
        fa: 'فعال',
        sr: 'Omogući',
        sk: 'Zapnúť',
        ja: '有効',
        pl: 'Włączenie',
        fi: 'Aktivoi',
        vi: 'Bật',
        nl: 'Inschakelen',
        ko: '활성화',
        bg: 'Включи',
      },
      disable: {
        en: 'Disable',
        zh: '关掉',
        zh_tw: '關閉',
        uk: 'Вимкнути',
        ru: 'Выключить',
        be: 'Выключыць',
        de: 'Deaktivieren',
        pt_br: 'Desabilitar',
        es: 'Deshabilitar',
        es_419: 'Deshabilitar',
        tr: 'Devre Dışı',
        th: 'ปิดการใช้งาน',
        fr: 'Désactiver',
        he: 'להשבית',
        hi: 'अक्षम करें',
        hu: 'Letilt',
        it: 'Disabilita',
        id: 'Matikan',
        cs: 'Vypnout',
        el: 'Απερνογοποιήση',
        ar: 'تعطيل',
        fa: 'غیرفعال',
        sr: 'Onemogući',
        sk: 'Vypnúť',
        ja: '無効',
        pl: 'Wyłączenie',
        fi: 'Deaktivoi',
        vi: 'Tắt',
        nl: 'Uitschakelen',
        ko: '비활성화',
        bg: 'Изключи',
      },
      converting: {
        en: 'Converting...',
        zh: '转换中······',
        zh_tw: '轉換中······',
        uk: 'Конвертація...',
        ru: 'Преобразование...',
        be: 'Канвертацыя...',
        de: 'Konvertierung...',
        pt_br: 'Convertendo...',
        es: 'Convirtiendo...',
        es_419: 'Generando...',
        tr: 'Dönüştürülüyor...',
        th: 'กำลังแปลง...',
        fr: 'Conversion...',
        he: 'ממיר...',
        hi: 'रूपांतरण हो रहा है...',
        hu: 'Átalakítás...',
        it: 'Conversione in corso...',
        id: 'Mengkonversi...',
        cs: 'Probíhá konverze...',
        el: 'Μετατροπή...',
        ar: 'التحويل...',
        fa: 'در حال تبدیل...',
        sr: 'Konvertovanje...',
        sk: 'Konvertuje sa...',
        ja: '変換中...',
        pl: 'Konwertowanie...',
        fi: 'Muunnetaan...',
        vi: 'Chuyển đổi...',
        nl: 'Omzetten',
        ko: '변환 중...',
        bg: 'Конвертиране...',
      },
      clearOutput: {
        en: 'Clear Output',
        zh: '清除输出f',
        zh_tw: '清除輸出',
        uk: 'Очистка Виводу',
        ru: 'Отчистить Вывод',
        be: 'Ачысціць Вывад',
        de: 'Ausgabe löschen',
        pt_br: 'Limpar Saída',
        es: 'Limpiar Salida',
        es_419: 'Limpiar Salida',
        tr: 'Çıktıyı Temizle',
        th: 'Clear Output',
        fr: 'Effacer la sortie',
        he: 'ניקוי פלט',
        hi: 'आउटपुट साफ़ करें',
        hu: 'Kimenet törlése',
        it: 'Cancella output',
        id: 'Clear Output',
        cs: 'Smazat výstup',
        el: 'Καθαρισμός Εξόδου',
        ar: 'مسح المخرجات',
        fa: 'پاکسازی خروجی',
        sr: 'Očisti izlaz',
        sk: 'Vyčistiť výstup',
        ja: '出力のクリア',
        pl: 'Wyczyść wyjście',
        fi: 'Tyhjennä ulostulo',
        vi: 'Xóa đầu ra',
        nl: 'Wis Uitkomst',
        ko: '출력 결과 지우기',
        bg: 'Изчистване на изхода',
      },
    },
    modal: {
      configModalTitle: {
        en: 'Override current configuration?',
        zh: '覆盖当前配置？',
        zh_tw: '覆蓋當前配置？',
        uk: 'Перезапис активних налаштувань',
        ru: 'Перезаписать текущие настройки?',
        be: 'Перазапісаць бягучыя налады?',
        de: 'Aktuelle Konfiguration überschreiben?',
        pt_br: 'Substituir configuração atual?',
        es: 'Sustituir configuración actual?',
        es_419: '¿Sustituir configuración actual?',
        tr: 'Geçerli yapılandırma geçersiz kılınsın mı?',
        th: 'เขียนทับการกำหนดค่าปัจจุบัน?',
        fr: 'Remplacer la configuration actuelle ?',
        he: 'לדרוס הגדרות קיימות?',
        hi: 'वर्तमान विन्यास को अधिरोधित करें?',
        hu: 'Felülírja a jelenlegi konfigurációt?',
        it: 'Sovrascrivi la confugurazione corrente?',
        id: 'Timpa konfigurasi saat ini?',
        cs: 'Přepsat aktuální konfiguraci?',
        el: 'Εγγραφή ρυθμίσεων πάνω στις υπάρχουσες ρυθμίσεις;',
        ar: 'هل تريد تجاوز التكوين الحالي؟',
        fa: 'تنظیمات فعلی را جایگزین کنید؟',
        sr: 'Zameniti trenutnu konfiguraciju?',
        sk: 'Prepísať aktuálnu konfiguráciu?',
        ja: '現在の構成を上書きしてもよろしいですか？',
        pl: 'Zastąpić aktualną konfigurację?',
        fi: 'Korvaa nykyinen konfiguraatio?',
        vi: 'Ghi đè lên cấu hình hiện tại?',
        nl: 'Overschrijf huidige configuratie?',
        ko: '현재 설정을 덮어씌우시겠습니까?',
        bg: 'Презаписване на сегашната конфигурация?',
      },
      configModalDescription: {
        en: 'All previously inserted values will be erased.',
        zh: '所有先前插入的值将被删除。',
        zh_tw: '所有先前插入的值將被刪除。',
        uk: 'Всі попередні налаштування будуть видалені.',
        ru: 'Все ранее текущие значения будут удалены.',
        be: 'Усе папярэднія бягучыя значэнні будуць выдаленыя.',
        de: 'Alle zuvor eingegebenen Werte werden gelöscht.',
        pt_br: 'Todos os valores inseridos anteriormente serão apagados.',
        es: 'Todos los valores insertados previamente serán borrados',
        es_419: 'Todos los valores insertados previamente serán borrados',
        tr: 'Önceden girilen tüm değerler silinecek.',
        th: 'ค่าที่ใส่ไว้ก่อนหน้านี้จะถูกลบ',
        fr: 'Toutes les valeurs précédemment insérées seront effacées.',
        he: 'כל הערכים שהוכנסו קודם לכן יימחקו',
        hi: 'पिछले में डाले गए सभी मान मिटा दिए जाएंगे।',
        hu: 'Minden korábbi érték törtlődik',
        it: 'Tutti i valori inseriti sarranno cancellati.',
        id: 'Semua nilai yang sebelumnya dimasukkan akan dihapus.',
        cs: 'Všechny předchozí hodnoty budou smazány.',
        el: 'Όλες οι προηγούμενες τιμές θα σβηστούν.',
        ar: 'سيتم مسح جميع القيم المدرجة مسبقًا.',
        fa: 'تمامی مقادیر درج شده، حذف خواهند شد.',
        sr: 'Sve prethodne vrednosti će biti izbrisane.',
        sk: 'Všetky aktuálne hodnoty budú vymazané.',
        ja: '挿入されている値は全て削除されます。',
        pl: 'Wszystkie wcześniej wprowadzone wartości zostaną wymazane.',
        fi: 'Kaikki aikaisemmin syötetyt arvot nollataan.',
        vi: 'Tất cả các giá trị trước đó sẽ bị xóa.',
        nl: 'Alle voorgaande ingegeven waarden worden gewist.',
        ko: '이전까지 입력된 값이 모두 삭제됩니다.',
        bg: 'Всички предварително зададени параметри ще бъдат изтрити.',
      },
      configModalConfirmButton: {
        en: 'Confirm',
        zh: '确认',
        zh_tw: '確認',
        uk: 'Підтвердити',
        ru: 'Подтвердить',
        be: 'Пацвердзіць',
        de: 'Bestätigen',
        pt_br: 'Confirmar',
        es: 'Confirmar',
        es_419: 'Confirmar',
        tr: 'Onayla',
        th: 'ยืนยัน',
        fr: 'confimer',
        he: 'אישור',
        hi: 'पुष्टि करें',
        hu: 'Megerősítés',
        it: 'Conferma',
        id: 'Konfirmasi',
        cs: 'Potvrdit',
        el: 'Επιβεβαίωση',
        ar: 'أكد',
        fa: 'تایید',
        sr: 'Potvrdi',
        sk: 'Potvrdiť',
        ja: '確認',
        pl: 'Potwierdź',
        fi: 'Vahvista',
        vi: 'Xác nhận',
        nl: 'Bevestig',
        ko: '확인',
        bg: 'Потвърди',
      },
      configModalCancelButton: {
        en: 'Cancel',
        zh: '取消',
        zh_tw: '取消',
        uk: 'Відміна',
        ru: 'Отмена',
        be: 'Скасаваць',
        de: 'Abbrechen',
        pt_br: 'Cancelar',
        es: 'Cancelar',
        es_419: 'Cancelar',
        tr: 'İptal',
        th: 'ยกเลิก',
        fr: 'Annuler',
        he: 'ביטול',
        hi: 'रद्द करें',
        hu: 'Mégse',
        it: 'Annulla',
        id: 'Batal',
        cs: 'Zrušit',
        el: 'Ακύρωση',
        ar: 'إلغاء',
        fa: 'لغو',
        sr: 'Otkaži',
        sk: 'Zrušiť',
        ja: 'キャンセル',
        pl: 'Anuluj',
        fi: 'Peruuta',
        vi: 'Hủy bỏ',
        nl: 'Annuleer',
        ko: '취소',
        bg: 'Отмяна',
      },
    },
  },
  // Things like alerts
  nonDom: {
    alert: {
      noScriptsLocationProvided: {
        en: 'You have not provided your scripts location.\nPlease enter this at the top of the page.',
        zh: '您尚未提供脚本位置。\n请在页面顶部输入。',
        zh_tw: '您尚未提供腳本位置。\n請在頁面頂部輸入。',
        uk: 'Ви не вказали місцезнаходження скрипта.\nВкажіть його на початку сторінки.',
        ru: 'Вы не указали расположение скрипта.\nУкажите его в самом верху страницы',
        be: 'Вы не пазначылі змесцаванне скрыпту.\nПазначце яго ўверсе старонкі',
        de: 'Kein Pfad zum Skript angegeben.\nBitte den Pfad zum Skript oben in der Seite eingeben.',
        pt_br: 'Você não informou a localização dos seus scripts.\nPor favor, insira isso no topo da página.',
        es: 'No ha facilitado la ubicación de sus guiones. \nPor favor, introdúzcala en la parte superior de la página.',
        es_419:
          'No ha facilitado la ubicación de sus guiones. \nPor favor, introdúzcala en la parte superior de la página.',
        tr: 'Komut dosyanızın konumunu belirtmediniz.\nLütfen bunu sayfanın en üstüne girin.',
        th: 'คุณยังไม่ได้ระบุตำแหน่งสคริปต์ของคุณ\nโปรดป้อนข้อมูลนี้ที่ด้านบนของหน้า',
        fr: "Vous n'avez pas fourni l'emplacement de vos scripts.\nVeuillez le saisir en haut de la page.",
        he: 'לא סיפקת את מיקום הסקריפטים שלך.\nאנא הזן את זה בראש הדף',
        hi: 'आपने अपनी स्क्रिप्ट की स्थान नहीं दी है। \n कृपया पृष्ठ के शीर्ष पर इसे दर्ज करें।',
        hu: 'Nem adtad meg a szkript elérési útvonalát.\nKérlek add meg az oldal tetején.',
        it: "Non hai provvisto la posizione dei tuoi script.\nPerfavore inserisci questo all'inizio della pagina",
        id: 'Anda belum menyediakan lokasi skrip Anda.\nSilakan masukkan di atas halaman.',
        cs: 'Chybí místo skriptu.\nProsím přidejte místo nahoře.',
        el: 'Δεν έχετε δώσει την τοποθεσία των αρχείων.\nΠαρακαλώ δώστε την τοποθεσία στην αρχή της σελίδας.',
        ar: 'لم تقم بتوفير موقع البرامج النصية الخاصة بك.  n الرجاء إدخال هذا في أعلى الصفحة.',
        fa: 'شما مکان اسکریپت های خود را ارائه نکرده اید.\nلطفاً مکان را در قسمت بالای صفحه وارد کنید.',
        sr: 'Nisi naveo lokaciju tvoje skripte.\nNavedi lokaciju na vrhu stranice',
        sk: 'Nezadali ste lokácia skriptu.\nPridajte lokáciu skriptu na vrchu formulára.',
        ja: 'スクリプトの位置が入力されていません。\nページ上部に入力してください。',
        pl: 'Nie podałeś swojej lokalizacji skryptów.\nProszę wpisać ją na górze strony.',
        fi: 'Et ole antanut skriptien sijaintia.\nSyötä se sivun yläpäässä.',
        vi: 'Thiếu không gian tập lệnh.\nVui lòng thêm không gian ở trên.',
        nl: 'Je hebt de locatie van je script niet opgegeven.\n Voeg deze in bovenaan de pagina.',
        ko: '스크립트 위치를 지정하지 않았습니다.\n이 페이지 맨 상단에서 위치를 지정하세요.',
        bg: 'Няма зададено местонахождение на скрипта. \nМоля задайте го в горната част на страницата.',
      },
      overwritePreviousOutput: {
        en: 'This action will overwrite a previous output in the output folder.\nContinue?',
        zh: '此操作将覆盖输出文件夹中以前的输出。\n是否继续？',
        zh_tw: '此操作將覆蓋輸出檔案夾中以前的輸出。\n是否繼續？',
        uk: 'Ця дія перезапише поперденій вивід у кінцевій директорії.\nПродовжуємо?',
        ru: 'Это действие перезапишет предыдущий вывод в папке вывода.\nПродолжить?',
        be: 'Гэтая дзея перазапіша папярэдні вывад у тэчцы вываду.\nПрацягнуць?',
        de: 'Diese Aktion wird vorige Ausgaben im Ausgabeverzeichnis überschreiben.\nFortsetzen?',
        pt_br: 'Essa ação irá sobrescrever uma saída anterior na pasta de saída.\nContinuar?',
        es: 'Esta acción sobrescribirá una salida anterior en la carpeta de salida.\n¿Continuar?',
        es_419: 'Esta acción sobrescribirá una salida anterior en la carpeta de salida.\n¿Desea continuar?',
        tr: 'Bu eylem, çıktı klasöründeki önceki bir çıktının üzerine yazacak.\nDevam edilsin mi?',
        th: 'การดำเนินการนี้จะเขียนทับเอาต์พุตก่อนหน้าในโฟลเดอร์เอาต์พุต\nทำต่อหรือไม่',
        fr: 'Cette action écrasera une sortie précédente dans le dossier de sortie.\nContinuer ?',
        he: 'פעולה זו תדרוס את הפלט בתיקיה של הפעולה הקודמת. האם להמשיך?',
        hi: 'यह कार्रवाई आउटपुट फ़ोल्डर में पिछले आउटपुट को अधिलेखित कर देगी। \n क्या आप जारी रखना चाहते हैं?',
        hu: 'Ez felülírja a legutóbbi kimenetet a kimeneti mappában.\nFolytatja?',
        it: 'Questa azione sovrascriverà un output precedente nella cartella di output.\nContinuare?',
        id: 'Tindakan ini akan menimpa sebuah output sebelumnya di folder output.\nLanjutkan?',
        cs: 'Toto přepíše předchozí výstup.\nPokračovat?',
        el: 'Αυτή η ενέργεια θα διαγράψει τιην προηγούμενη έξοδο αρχείου στον φάκελο εξόδου.\nΝα συνεχίσω;',
        ar: 'سيؤدي هذا الإجراء إلى الكتابة فوق الإخراج السابق في مجلد الإخراج. \nهل تريد المتابعة؟',
        fa: 'این کار فایل خروجی قبلی را در فایل خروجی بازنویسی می کند\nادامه میدهید؟',
        sr: 'Ovo će zameniti prethodni izlaz u izlaznom direktorijumu.\nNastavi?',
        sk: 'Toto prepíše aktuálny výstup.\nPokračovať?',
        ja: 'この操作により、出力フォルダー内の以前の出力が上書きされます。\n続けてもよろしいですか？',
        pl: 'Ta akcja spowoduje nadpisanie poprzedniego katalogu wyjściowego na nowy katalog wyjściowy.\nKontynować?',
        fi: 'Tämä toiminto korvaa aikaisemman ulostulon ulostulokansiosta.\nOletko varma?',
        vi: 'Điều này sẽ ghi đè lên đầu ra trước đó.\nTiếp tục?',
        nl: 'Deze actie overschrijft de vorige uitkomst in de uitvoer map.\n Doorgaan?',
        ko: '이 작업은 출력 폴더에 과거의 출력물을 덮어씌웁니다.\n계속하시겠습니까?',
        bg: 'Това действие ще презапише предходният изход в изходната папка. \nИскате ли да продължите?',
      },
    },
  },
};

const translate = (language) => {
  if (language === undefined) {
    language = currentLanguage;
  }

  // Check that the language is supported
  language = _checkLanguageIsSupportedOrDefault(language);
  currentLanguage = language;

  // Update the language selector incase this call didn't come from the dropdown
  const languageSelectNode = document.getElementById('language-selection');
  languageSelectNode.value = currentLanguage;

  // Update text inside elements
  const elementsToTranslate = document.querySelectorAll('[data-i18n]');
  elementsToTranslate.forEach((element) => {
    const keys = element.dataset.i18n.split('.');
    const translationBlock = keys.reduce((obj, i) => obj[i], translationMap);

    // If there is a translation, translate otherwise use the default language
    if (translationBlock !== undefined && translationBlock[language] !== undefined) {
      element.innerHTML = translationBlock[language];
    } else {
      element.innerHTML = translationBlock[_defaultLanguage];
    }
  });

  // Update placeholders
  const placeholdersToTranslate = document.querySelectorAll('[data-i18n_placeholder]');
  placeholdersToTranslate.forEach((element) => {
    const keys = element.dataset.i18n_placeholder.split('.');
    const translationBlock = keys.reduce((obj, i) => obj[i], translationMap);

    if (translationBlock !== undefined && translationBlock[language] !== undefined) {
      element.placeholder = translationBlock[language];
    } else {
      element.placeholder = translationBlock[_defaultLanguage];
    }
  });

  // Update titles
  const titleToTranslate = document.querySelectorAll('[data-i18n_title]');
  titleToTranslate.forEach((element) => {
    const keys = element.dataset.i18n_title.split('.');
    const translationBlock = keys.reduce((obj, i) => obj[i], translationMap);

    if (translationBlock !== undefined && translationBlock[language] !== undefined) {
      element.title = translationBlock[language];
    } else {
      element.title = translationBlock[_defaultLanguage];
    }
  });
};

const _getLanguage = () => {
  const language =
    (navigator.languages && navigator.languages[0]) || // Chrome / Firefox
    navigator.language || // All browsers
    navigator.userLanguage; // IE <= 10

  let shortLang = language;
  if (shortLang.indexOf('-') !== -1) {
    shortLang = shortLang.split('-')[0];
  }
  if (shortLang.indexOf('_') !== -1) {
    shortLang = shortLang.split('_')[0];
  }

  return shortLang;
};

const _checkLanguageIsSupportedOrDefault = (language) => {
  if (supportedLanguages.map((x) => x.code).indexOf(language) !== -1) {
    return language;
  } else {
    return _defaultLanguage;
  }
};

const getTranslation = (path) => {
  const keys = path.split('.');
  const translationBlock = keys.reduce((obj, i) => obj[i], translationMap);

  if (translationBlock !== undefined && translationBlock[currentLanguage] !== undefined) {
    return translationBlock[currentLanguage];
  } else {
    return translationBlock[_defaultLanguage];
  }
};

const _defaultLanguage = 'en';
const supportedLanguages = [
  {
    name: 'Arabic (العربية)',
    code: 'ar',
  },
  {
    name: 'Belarusian (Беларуская)',
    code: 'be',
  },
  {
    name: 'Brazilian Portuguese (Português Brasileiro)',
    code: 'pt_br',
  },
  {
    name: 'Bulgarian (Български)',
    code: 'bg',
  },
  {
    name: 'Chinese Simplified (简体中文)',
    code: 'zh',
  },
  {
    name: 'Chinese Traditional (繁體中文)',
    code: 'zh_tw',
  },
  {
    name: 'Czech (Čeština)',
    code: 'cs',
  },
  {
    name: 'Dutch (Nederlands)',
    code: 'nl',
  },
  {
    name: 'English',
    code: 'en',
  },
  {
    name: 'Finnish (Suomen kieli)',
    code: 'fi',
  },
  {
    name: 'French (Français)',
    code: 'fr',
  },
  {
    name: 'German (Deutsch)',
    code: 'de',
  },
  {
    name: 'Greek (Ελληνικά)',
    code: 'el',
  },
  {
    name: 'Hebrew (עברית)',
    code: 'he',
  },
  {
    name: 'Hindi (हिंदी)',
    code: 'hi',
  },
  {
    name: 'Hungarian (Magyar)',
    code: 'hu',
  },
  {
    name: 'Indonesian (Bahasa Indonesia)',
    code: 'id',
  },
  {
    name: 'Italian (Italiano)',
    code: 'it',
  },
  {
    name: 'Japanese (日本語)',
    code: 'ja',
  },
  {
    name: 'Korean (한국어)',
    code: 'ko',
  },
  {
    name: 'Persian (فارسی)',
    code: 'fa',
  },
  {
    name: 'Polish (Polski)',
    code: 'pl',
  },
  {
    name: 'Russian (Русский)',
    code: 'ru',
  },
  {
    name: 'Serbian (Srpski)',
    code: 'sr',
  },
  {
    name: 'Slovak (Slovenčina)',
    code: 'sk',
  },
  {
    name: 'Spanish (Español)',
    code: 'es',
  },
  {
    name: 'Spanish Latin America (Español Latam)',
    code: 'es_419',
  },
  {
    name: 'Thai (ภาษาไทย)',
    code: 'th',
  },
  {
    name: 'Turkish (Türkçe)',
    code: 'tr',
  },
  {
    name: 'Ukrainian (Українська)',
    code: 'uk',
  },
  {
    name: 'Vietnamese (Tiếng Việt)',
    code: 'vi',
  },
];

let currentLanguage = _checkLanguageIsSupportedOrDefault(_getLanguage()); // Keeps track of the current language
