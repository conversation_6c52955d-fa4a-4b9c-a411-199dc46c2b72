# 网络设备自动化巡检系统

## 项目概述

这是一个基于Python的网络设备自动化巡检系统，支持多厂商网络设备的批量连接测试、配置采集、健康检查等功能。系统通过Excel模板配置设备信息和巡检命令，支持并发执行，自动生成报告并可通过邮件发送结果。

## 核心特性

- 🔧 **多厂商支持**: 支持Cisco、华为、ALE、锐捷、H3C等主流厂商设备
- 📊 **Excel配置**: 通过Excel模板配置设备信息和巡检命令，简单易用
- ⚡ **并发执行**: 支持多线程并发执行，大幅提升巡检效率
- 📝 **自动报告**: 自动生成巡检报告和汇总统计
- 📦 **结果打包**: 自动压缩巡检结果，便于存储和传输
- 📧 **邮件通知**: 支持邮件自动发送巡检结果
- 🔍 **连接测试**: 独立的设备连接测试功能
- 📋 **日志记录**: 详细的日志记录和错误追踪

## 快速开始

### 1. 环境准备
```bash
# 确保Python版本 >= 3.7
python --version

# 安装依赖
pip install -r requirements.txt
```

### 2. 一键启动（推荐）
```bash
python start.py
```
启动脚本会自动检查环境、创建模板、启动系统。

### 3. 演示模式
```bash
python demo.py
```
运行演示脚本了解系统功能。

## 文件结构

```
├── network_inspection.py    # 主程序 - 完整巡检流程
├── connect.py              # 核心模块 - 设备连接和命令执行
├── template_manager.py     # 模板管理 - Excel模板创建和验证
├── start.py                # 启动脚本 - 环境检查和系统启动
├── demo.py                 # 演示脚本 - 功能演示
├── send_email.py           # 邮件模块 - 结果邮件发送
├── zip_file.py             # 压缩模块 - 结果打包压缩
├── requirements.txt        # 依赖列表
├── template.xlsx           # 设备配置模板
├── 使用说明.md             # 详细使用说明
└── LOG/                    # 日志和结果目录
```

## Excel模板配置

### 设备信息工作表
| 列名 | 说明 | 示例 |
|------|------|------|
| 序号 | 设备编号 | 1 |
| 状态 | 启用状态，填"#"跳过 | 启用 |
| 设备IP | 管理IP地址 | *********** |
| 协议 | 连接协议 | ssh |
| 端口 | 连接端口 | 22 |
| 用户名 | 登录用户名 | admin |
| 密码 | 登录密码 | password |
| 特权密码 | enable密码(可选) | enable_pass |
| 设备类型 | netmiko设备类型 | cisco_ios |

### 支持的设备类型
- **cisco_ios**: Cisco IOS设备
- **huawei**: 华为设备  
- **alcatel_aos**: ALE设备
- **ruijie_os**: 锐捷设备
- **hp_comware**: H3C设备

### 命令配置工作表
每种设备类型对应一个工作表，配置该类型设备的巡检命令：
- 状态列：填"#"跳过该命令
- 命令列：要执行的CLI命令

## 使用方法

### 交互式模式
```bash
python network_inspection.py
```
选择功能菜单：
1. 设备连接测试
2. 设备信息采集  
3. 完整巡检流程
4. 压缩现有结果
5. 清理旧日志

### 命令行模式
```bash
# 完整巡检
python network_inspection.py --auto

# 仅连接测试
python network_inspection.py --test

# 仅信息采集
python network_inspection.py --collect
```

### 模板管理
```bash
python template_manager.py
```
1. 创建新模板
2. 验证现有模板

## 输出结果

### 目录结构
```
LOG/
├── YYYYMMDD_HHMMSS/           # 按时间戳分组的巡检结果
│   ├── ***********_Router01/  # 按设备分组
│   │   ├── show version.conf
│   │   ├── show running-config.conf
│   │   └── ...
│   └── 192.168.1.2_Switch01/
│       └── ...
├── inspection_summary_YYYYMMDD_HHMMSS.txt  # 汇总报告
├── inspection_YYYYMMDD_HHMMSS.log          # 主程序日志
├── error_YYYYMMDD_HHMMSS.log               # 错误日志
└── connect_t_YYYYMMDD_HHMMSS.log           # 连接测试日志
```

### 压缩文件
- `inspection_results_YYYYMMDD_HHMMSS.zip` - 完整结果压缩包

## 高级配置

### 并发数调整
在 `connect.py` 中修改：
```python
self.pool = ThreadPool(10)  # 调整并发线程数
```

### 超时时间调整
```python
connect = ConnectHandler(**host, conn_timeout=15)  # 调整连接超时
```

### 邮件配置
编辑 `send_email.py` 配置SMTP服务器信息。

## 常见问题

### 连接问题
- 检查网络连通性
- 确认设备IP和端口
- 检查防火墙设置

### 认证问题  
- 确认用户名密码
- 检查设备SSH/Telnet服务
- 确认用户权限

### 命令执行问题
- 检查命令语法
- 确认用户权限
- 验证设备类型配置

## 技术栈

- **Python 3.7+**: 主要开发语言
- **netmiko**: 网络设备连接库
- **openpyxl**: Excel文件处理
- **prettytable**: 表格格式化输出
- **pandas**: 数据处理
- **paramiko**: SSH客户端

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 更新日志

- **v1.0**: 初始版本，支持基本巡检功能
- **v1.1**: 添加模板管理和启动脚本
- **v1.2**: 优化错误处理和日志记录
- **v1.3**: 添加演示脚本和详细文档

---

**注意**: 请在生产环境使用前充分测试，确保配置正确且网络安全。
