
=======
LAYER 3
=======

#########################################
show ip routes
#########################################

 + = Equal cost multipath routes
 Total 6 routes

  Dest Address       Gateway Addr        Age        Protocol 
------------------+-------------------+----------+-----------
  0.0.0.0/0            ************        18d21h   STATIC    
  *******/32           ************         3d 2h   OSPF      
  *******/32           *******             18d21h   LOCAL     
  **********/24        ***********         18d21h   LOCAL     
  127.0.0.1/32         127.0.0.1           18d21h   LOCAL     
  *************/24     **************      18d21h   LOCAL     


#########################################
show ip router database
#########################################
Legend: + indicates routes in-use
        b indicates BFD-enabled static route
        i indicates interface static route
        r indicates recursive static route, with following address in brackets

Total IPRM IPv4 routes: 6

  Destination         Gateway                   Interface              Protocol  Metric     Tag      Misc-Info
---------------------+---------------+--------------------------------+--------+-------+----------+-----------------
+  0.0.0.0/0          ************    vlan1                            STATIC         1          0  
+  *******/32         ************    vlan1                            OSPF           2          0  
+  *******/32         *******         Loopback0                        LOCAL          1          0  
+  **********/24      ***********     vlan1                            LOCAL          1          0  
+  127.0.0.1/32       127.0.0.1       Loopback                         LOCAL          1          0  
+  *************/24   **************  vlan10                           LOCAL          1          0  


Inactive Static Routes
   Destination       Gateway           Metric        Tag   Misc-Info
--------------------+-----------------+------+----------+-----------------


#########################################
show ip access-list
#########################################
Access Lists: ipv4 configured: 0 ipv6 configured: 0 total configured: 0 max: 0

                     Address /                  Redistribution
Name                 Prefix Length      Effect  Control
--------------------+------------------+-------+--------------


#########################################
show ip route-map
#########################################
Route Maps: configured: 0 max: 200


#########################################
show ip redist
#########################################

Source       Destination
Protocol     Protocol     Admin State  Route Map
------------+------------+-----------+--------------------


#########################################
show ip traffic
#########################################

Summary:
Datagrams received
  Total                   =        108,
  IP header error         =          0,
  Destination IP error    =         94,
  Unknown protocol        =          0,
  Local discards          =         14,
  Delivered to users      =          0,
  Reassemble needed       =          0,
  Reassembled             =          0,
  Reassemble failed       =          0

Datagrams sent
  Forwarded               =          0,
  Generated               =          0,
  Local discards          =          0,
  No route discards       =          0,
  Fragmented              =          0,
  Fragment failed         =          0,
  Fragments generated     =          0

Chassis 1 Slot 1:
Datagrams received
  Total                   =        108,
  IP header error         =          0,
  Destination IP error    =         94,
  Unknown protocol        =          0,
  Local discards          =         14,
  Delivered to users      =          0,
  Reassemble needed       =          0,
  Reassembled             =          0,
  Reassemble failed       =          0


Datagrams sent
  Forwarded               =          0,
  Generated               =          0,
  Local discards          =          0,
  No route discards       =          0,
  Fragmented              =          0,
  Fragment failed         =          0,
  Fragments generated     =          0



#########################################
show icmp statistics
#########################################

  Messages                    Received         Sent
---------------------------+----------+-------------
  Total                             40      5459329
  Error                             14            0
  Destination unreachable           40      5459329
  Time exceeded                      0            0
  Parameter problem                  0            0
  Source quench                      0            0
  Redirect                           0            0
  Echo request                       0            0
  Echo reply                         0            0
  Address mask request               0            0
  Address mask reply                 0            0


#########################################
show tcp statistics
#########################################

Total segments received =    374306202,
Error segments received =            0,
Total segments sent     =    375627065,
Segments retransmitted  =      4240595,
Reset segments sent     =          932,
Connections initiated   =      1625996,
Connections accepted    =        37057,
Connections established =         1463,
Attempt fails           =        12514,
Established resets      =           32



#########################################
show tcp ports
#########################################

  Local Address     Local Port   Remote Address    Remote Port    State       
------------------+------------+-----------------+--------------+-------------
  0.0.0.0                   22   0.0.0.0                     0    LISTEN        
  0.0.0.0                   80   0.0.0.0                     0    LISTEN        
  0.0.0.0                  111   0.0.0.0                     0    LISTEN        
  0.0.0.0                  443   0.0.0.0                     0    LISTEN        
  0.0.0.0                 2049   0.0.0.0                     0    LISTEN        
  0.0.0.0                10161   0.0.0.0                     0    LISTEN        
  0.0.0.0                32050   0.0.0.0                     0    LISTEN        
  0.0.0.0                32051   0.0.0.0                     0    LISTEN        
  *******                10043   0.0.0.0                     0    LISTEN        
  ***********               22   ************            54657    ESTABLISHED   
  ***********               22   ************            54658    ESTABLISHED   



#########################################
show udp statistics
#########################################

Total datagrams received   =       816136,
Error datagrams received   =            0,
No port datagrams received =          335,
Total datagrams sent       =       840158



#########################################
show udp ports
#########################################

                  Local Address                  Local Port   
----------------------------------------------+--------------
  0.0.0.0                                                67
  0.0.0.0                                               111
  0.0.0.0                                               123
  0.0.0.0                                               161
  0.0.0.0                                              1018
  0.0.0.0                                              2049
  0.0.0.0                                              3799
  0.0.0.0                                             32050
  0.0.0.0                                             32051
  0.0.0.0                                             32985
  0.0.0.0                                             35637
  0.0.0.0                                             38223
  0.0.0.0                                             41105
  *******                                               123
  ***********                                           123
  ***********                                           514
  10.10.10.255                                          123
  **************                                        123
  ***************                                       123
  ::                                                    123
  ::                                                    161
  ::                                                  32051
  ::                                                  35469
  ::1                                                   123



#########################################
show ip vrrp
#########################################
VRRP default advertisement interval: 100 centiseconds
VRRP default priority: 100
VRRP default preempt: Yes
VRRP default accept: Yes
VRRP default version: V2
VRRP startup delay: 45  (expired)
VRRP BFD-STATE : Disabled 

                Interface                 IPv4                 Admin                             Adv.  
VRID              Name                 Address(es)    Version  Status  Priority Preempt Accept Interval
----+--------------------------------+---------------+-------+--------+--------+-------+------+--------
  30 vlan30                           **************     V2   Enabled       100     Yes     NA      100


#########################################
show ip vrrp statistics
#########################################
Checksum Errors :          0,
Version Errors  :          0,
VRID Errors     :          0

                Interface                                                           
VRID              Name                  State      UpTime   Become Master Adv. Rcvd 
----+--------------------------------+----------+----------+-------------+----------
  30 <USER>                           <GROUP>          0            0           0


#########################################
show arp
#########################################

Total 4 arp entries
 Flags (P=Proxy, A=Authentication, V=VRRP, B=BFD, H=HAVLAN, I=INTF, M=Managed)

 IP Addr           Hardware Addr       Type       Flags   Port              Interface   Name
-----------------+-------------------+----------+-------+-----------------+-----------+---------------------------------
 ************      b0:5e:0e:9f:00:20   DYNAMIC                       1/1/1  vlan1                                       
 ************      50:00:00:02:00:00   DYNAMIC                      1/1/17  vlan1                                       
 ************      30:43:d7:ef:59:79   DYNAMIC                      1/1/19  vlan1                                       
 ************      52:54:00:f2:ca:24   DYNAMIC                      1/1/17  vlan1                                       



#########################################
show qos log
#########################################
**QOS Log**
 2/15/25 15:54:13 Log Init (67b04825).
 2/15/25 15:54:13 qosMuxGroupRegisterApp: registered handler 0x580f68 for appid ab
 2/15/25 15:54:13 qosNIStateInit: allocated id 0 for all NI mux group.
 2/15/25 15:54:13 Reactor initialized
 2/15/25 15:54:13 MAC changed: 2c:fa:a2:62:9d:21
 2/15/25 15:54:13 qosNIMsgSendRtrMac: mac 2c:fa:a2:62:9d:21 send to 0:0, 0 nis
 2/15/25 15:54:13 QoS registered with Chassis Supervisor
 2/15/25 15:54:13 QoS registered with MIP library
 2/15/25 15:54:13 QoS registered with Port Manager
 2/15/25 15:54:13 QoS registered with Vlan Manager
 2/15/25 15:54:13 Got eoic (67b04825)
 2/15/25 15:54:13 Apply QoS configuration (cli)
 2/15/25 15:54:13 Calling cslib_unblock (67b04825)
 2/15/25 15:54:13 QoS registered with ipedr
 2/15/25 15:54:13 QoS registered with ipv6
 2/15/25 15:54:13 Connect from API 1/1
 2/15/25 15:54:13 Connect from API 1/2
 2/15/25 15:54:14 Connect from API 1/3
 2/15/25 15:54:14 Connect from API 1/4
 2/15/25 15:54:14 Connect from API 1/5
 2/15/25 15:54:14 Connect from API 1/6
 2/15/25 15:54:14 Connect from API 1/7
 2/15/25 15:54:14 Connect from API 1/8
 2/15/25 15:54:14 Connect from API 1/9
 2/15/25 15:54:15 Connect from API 1/10
 2/15/25 15:54:16 Connect from API 1/11
 2/15/25 15:54:16 Disabling ipmsv4 flood unknown
 2/15/25 15:54:16 Disabling ipmsv6 flood unknown
 2/15/25 15:54:16 ipms svp config enable4 0 enable6 0 nodrop4 0 nodrop6 0
 2/15/25 15:54:17 Connect from API 1/12
 2/15/25 15:54:17 Connect from API 1/13
 2/15/25 15:54:18 Connect from API 1/14
 2/15/25 15:54:21 Connect from API 1/15
 2/15/25 15:54:21 Connect from API 1/16
 2/15/25 15:54:21 Connect from API 0/1
 2/15/25 15:54:21 Connect from API 0/2
 2/15/25 15:54:22 Connect from API 0/3
 2/15/25 15:54:22 Connect from API 0/4
 2/15/25 15:54:22 Connect from API 0/5
 2/15/25 15:54:23 add VRF [id 0] name "default"
 2/15/25 15:54:26 Connect from API 0/6
 2/15/25 15:54:26 qosApiHandleOpenFlowMsg: got message ab0100 [CfgTables] 00000000
 2/15/25 15:54:26 muxid 1 allocated for OpenFlow.
 2/15/25 15:54:59 Connect from slot 1/1

 2/15/25 15:55:02 Connect reply from 1:1 (seq 0, insync 0: 80000000)
 2/15/25 15:55:14 NI 1/1 Up
 2/15/25 15:55:14 PM Link Status register for slot 1/1 ports 0 - 1d
 2/15/25 15:55:19 qosApiHandleOpenFlowMsg: got message ab0100 [CfgTables] 00000000
 2/15/25 15:55:29 Disabling ipmsv4 flood unknown
 2/15/25 15:55:29 Disabling ipmsv6 flood unknown
 2/15/25 15:55:29 ipms svp config enable4 0 enable6 0 nodrop4 0 nodrop6 0
 2/15/25 15:55:29 VC Takeover in progress (67b04871).
 2/15/25 15:55:29 qosSendSlotup: Sending Slotup message to API sockets.
 2/15/25 15:55:29 qosLDAPIdFileGet: LDAP Id is 2c:fa:a2:62:9d:21:20160924:180640
 2/15/25 15:55:29 VC Takeover complete (67b04871).
 2/15/25 15:55:29 qosSocketClose 233:parent socket API disconnected, fd 82
 2/15/25 15:55:29 Disconnect from API 1/6
 2/15/25 15:55:29 Connect from API 1/6
 2/15/25 15:55:29 Config sent to Slot 1/1
 2/15/25 15:55:29 Send 1 vpa status, 0 ip 0 ipas interface messages for slot 1
 2/15/25 15:55:29 Slot 1/1 Ready.
 2/15/25 15:55:29 qosNIMsgSendRtrMac: mac 2c:fa:a2:62:9d:21 send to 1:1, 1 nis
 2/15/25 15:55:29 qosApiHandleOpenFlowMsg: got message ab0100 [CfgTables] 00000000
 2/15/25 15:55:31 NI[1/1]: Apply QoS configuration (cli)
 2/15/25 15:55:31 NI[1/1]: Apply QoS configuration (cli)
 2/15/25 16:02:29 AppFp takeover timer handler running.


#########################################
show qos config
#########################################
QoS Configuration
  Admin                          = disable,
  Switch Group                   = expanded,
  Trust ports                    = no,
  Phones                         = trusted,
  Log lines                      = 10240,
  Log level                      = 6,
  Log console                    = no,
  Forward log                    = no,
  Stats interval                 = 60,
  User-port filter               = spoof,
  User-port shutdown             = none,
  Debug                          = info,
  DEI Mapping                    = Disabled,
  DEI Marking                    = Disabled,
  Pending changes                = none



#########################################
show qos port
#########################################
Slot/                 Default    Default               Bandwidth           DEI
Port    Active  Trust P/DSCP Classification  Physical  Ingress Egress    Map Mark   Type
-------+-------+-----+------+--------------+----------+-------+------+------+------+-------------
1/1/1     Yes      No  0/ 0           DSCP         1G       -      -     No    No   ethernet-1G
1/1/2      No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/3     Yes      No  0/ 0           DSCP         1G       -      -     No    No   ethernet-1G
1/1/4      No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/5     Yes      No  0/ 0           DSCP       100M       -      -     No    No   ethernet-100
1/1/6      No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/7      No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/8     Yes      No  0/ 0           DSCP         1G       -      -     No    No   ethernet-1G
1/1/9      No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/10    Yes      No  0/ 0           DSCP         1G       -      -     No    No   ethernet-1G
1/1/11    Yes      No  0/ 0           DSCP         1G       -      -     No    No   ethernet-1G
1/1/12     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/13    Yes      No  0/ 0           DSCP         1G       -      -     No    No   ethernet-1G
1/1/14     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/15     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/16     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/17    Yes      No  0/ 0           DSCP         1G       -      -     No    No   ethernet-1G
1/1/18     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/19    Yes      No  0/ 0           DSCP         1G       -      -     No    No   ethernet-1G
1/1/20     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/21     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/22     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/23     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/24     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/25     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/26     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/27    Yes      No  0/ 0           DSCP        10G       -      -     No    No   ethernet-10G
1/1/28     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/29     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet
1/1/30     No      No  0/ 0           DSCP         0K       -      -     No    No   ethernet


#########################################
show qos slice
#########################################
Slot/                  Ranges          Rules          Counters         Meters
Unit            Type Total/Free  CAM Total/Free      Total/Free      Total/Free
 1/1/(0)        IFP    24/24       0   256/252          256/252         256/256 
                                   1   256/255          256/255         256/256 
                                   2   256/256          256/256         256/256 
                                   3   256/256          256/256         256/256 
                                   4   256/256          256/256         256/256 
                                   5   256/256          256/256         256/256 
                                   6   256/256          256/256         256/256 
                                   7   256/256          256/256         256/256 
                                   8   256/256          256/256         256/256 
                                   9   256/256          256/256         256/256 
                                  10   256/256          256/256         256/256 
                                  11   256/256          256/256         256/256 
                                  12   256/256          256/256         256/256 
                                  13   256/256          256/256         256/256 
                                  14   256/255          256/254         256/254 
                                  15   256/255          256/256         256/256 
 1/1/(0)        EFP     0/0        0   256/256          256/256         256/256 
                                   1   256/256          256/256         256/256 
                                   2   256/256          256/256         256/256 
                                   3   256/256          256/256         256/256 



#########################################
show qos statistics
#########################################
QoS stats
                            Events   Matches     Drops
  L2                   :         0         0         0
  L3 Inbound           :         0         0         0
  L3 Outbound          :         0         0         0
  IGMP Join            :         0         0         0
  Fragments            : 0
  Bad Fragments        : 0
  Unknown Fragments    : 0
  Sent NI messages     : 27364
  Received NI messages : 27180
  Failed NI messages   : 0
  Max PTree nodes      : 0
  Max PTree depth      : 0
  Spoofed Events       : 0
  NonSpoofed Events    : 0

Software resources
                       Applied                 Pending
           Table  CLI LDAP  Blt Total     CLI LDAP  Blt Total     Max
           rules    0    0    0     0       0    0    0     0    8192
         actions    0    0    0     0       0    0    0     0    8192
      conditions    0    0    0     0       0    0    0     0    8192
        services    0    0    0     0       0    0    0     0     256
  service groups    0    0    0     0       0    0    0     0    1024
  network groups    0    0    3     3       0    0    3     3    1024
     port groups    1    0    0     1       1    0    0     1    1024
      mac groups    0    0    0     0       0    0    0     0    1024
      map groups    0    0    0     0       0    0    0     0    1024
validity periods    0    0    0     0       0    0    0     0      64

Hardware resources         TCAM               Ranges
   Slot Slice Unit   Used  Free   Max    Used  Free   Max
   1/ 1     0    0      0  3072  3072       0    24    24



#########################################
show policy server long
#########################################
No servers

#########################################
show policy server statistics
#########################################
No policy server statss

#########################################
show policy server rules
#########################################
No rulenames

#########################################
show policy server events
#########################################
 Event time                        event description
-----------------+------------------------------------------------------
02/15/25 15:54:13 Initialized policy event log
02/15/25 15:54:13 Initialized LDAP


#########################################
show active policy rule
#########################################
No active rules

#########################################
show active policy list
#########################################
No applied lists
