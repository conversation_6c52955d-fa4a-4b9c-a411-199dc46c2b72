#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
ALE设备巡检配置文件
用于配置FTP/SCP下载参数
"""

# ALE设备文件下载配置
ALE_DOWNLOAD_CONFIG = {
    # 下载方式优先级: scp > ftp > tftp
    'download_methods': ['scp', 'ftp', 'tftp'],
    
    # SCP配置
    'scp': {
        'enabled': True,
        'port': 22,
        'timeout': 30,
        # 文件路径（相对于设备根目录）
        'remote_path_prefix': '/',
    },
    
    # FTP配置
    'ftp': {
        'enabled': True,
        'port': 21,
        'timeout': 30,
        'passive_mode': True,
        # 文件路径（相对于FTP根目录）
        'remote_path_prefix': '/',
    },
    
    # TFTP配置
    'tftp': {
        'enabled': True,
        'port': 69,
        'timeout': 30,
        'block_size': 512,
    },
    
    # 认证配置
    'auth': {
        # 是否使用SSH设备的认证信息
        'use_ssh_credentials': True,
        
        # 备用认证信息（当SSH认证失败时使用）
        'fallback_credentials': {
            'username': 'admin',
            'password': 'admin',
        },
        
        # 特定设备的认证信息（可选）
        'device_specific': {
            # 示例：
            # '192.168.1.1': {
            #     'username': 'ale_admin',
            #     'password': 'ale_password',
            # },
        }
    },
    
    # 文件下载配置
    'files': {
        # tech-support生成的日志文件
        'tech_support_files': [
            'tech_support_layer3.log',
            'tech_support_layer2.log',
            'tech_support.log'
        ],
        
        # 等待文件生成的时间（秒）
        'wait_time_after_tech_support': 15,
        
        # 下载重试次数
        'download_retry_count': 3,
        
        # 重试间隔（秒）
        'retry_interval': 5,
    },
    
    # 错误处理配置
    'error_handling': {
        # 是否创建失败记录文件
        'create_failure_records': True,
        
        # 是否在下载失败时继续执行其他命令
        'continue_on_download_failure': True,
        
        # 是否显示详细错误信息
        'verbose_errors': True,
    }
}

def get_device_credentials(device_ip, ssh_username=None, ssh_password=None):
    """获取设备的认证信息"""
    config = ALE_DOWNLOAD_CONFIG['auth']
    
    # 1. 检查是否有设备特定的认证信息
    if device_ip in config['device_specific']:
        device_creds = config['device_specific'][device_ip]
        return device_creds['username'], device_creds['password']
    
    # 2. 使用SSH设备的认证信息
    if config['use_ssh_credentials'] and ssh_username and ssh_password:
        return ssh_username, ssh_password
    
    # 3. 使用备用认证信息
    fallback = config['fallback_credentials']
    return fallback['username'], fallback['password']

def get_download_config():
    """获取下载配置"""
    return ALE_DOWNLOAD_CONFIG

def update_device_credentials(device_ip, username, password):
    """更新特定设备的认证信息"""
    if 'device_specific' not in ALE_DOWNLOAD_CONFIG['auth']:
        ALE_DOWNLOAD_CONFIG['auth']['device_specific'] = {}
    
    ALE_DOWNLOAD_CONFIG['auth']['device_specific'][device_ip] = {
        'username': username,
        'password': password
    }
    
    print(f"已更新设备 {device_ip} 的认证信息")

def test_credentials(device_ip, username, password):
    """测试认证信息"""
    try:
        import paramiko
        
        print(f"测试SSH连接: {device_ip}")
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(device_ip, username=username, password=password, timeout=10)
        ssh.close()
        
        print(f"✓ SSH认证成功: {device_ip}")
        return True
        
    except Exception as e:
        print(f"✗ SSH认证失败: {device_ip} - {e}")
        return False

def main():
    """配置工具主函数"""
    print("ALE设备下载配置工具")
    print("=" * 40)
    
    while True:
        print("\n选择操作:")
        print("1. 查看当前配置")
        print("2. 测试设备认证")
        print("3. 更新设备认证信息")
        print("4. 查看认证信息")
        print("0. 退出")
        
        choice = input("\n请选择: ").strip()
        
        if choice == '1':
            print("\n当前配置:")
            config = get_download_config()
            print(f"下载方式: {config['download_methods']}")
            print(f"使用SSH认证: {config['auth']['use_ssh_credentials']}")
            print(f"备用用户名: {config['auth']['fallback_credentials']['username']}")
            print(f"等待时间: {config['files']['wait_time_after_tech_support']}秒")
            
        elif choice == '2':
            device_ip = input("请输入设备IP: ").strip()
            username = input("请输入用户名: ").strip()
            password = input("请输入密码: ").strip()
            
            if device_ip and username and password:
                test_credentials(device_ip, username, password)
            else:
                print("请输入完整信息")
                
        elif choice == '3':
            device_ip = input("请输入设备IP: ").strip()
            username = input("请输入用户名: ").strip()
            password = input("请输入密码: ").strip()
            
            if device_ip and username and password:
                update_device_credentials(device_ip, username, password)
            else:
                print("请输入完整信息")
                
        elif choice == '4':
            device_ip = input("请输入设备IP (回车查看所有): ").strip()
            
            if device_ip:
                username, password = get_device_credentials(device_ip)
                print(f"设备 {device_ip} 的认证信息:")
                print(f"用户名: {username}")
                print(f"密码: {password}")
            else:
                config = ALE_DOWNLOAD_CONFIG['auth']
                print("所有设备认证配置:")
                print(f"备用认证: {config['fallback_credentials']}")
                if config['device_specific']:
                    print("设备特定认证:")
                    for ip, creds in config['device_specific'].items():
                        print(f"  {ip}: {creds['username']}")
                        
        elif choice == '0':
            break
        else:
            print("无效选择")

if __name__ == '__main__':
    main()
