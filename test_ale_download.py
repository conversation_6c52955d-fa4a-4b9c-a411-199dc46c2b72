#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
ALE设备下载功能测试脚本
用于测试和调试文件下载功能
"""

import os
import sys
from datetime import datetime

def test_scp_download():
    """测试SCP下载功能"""
    print("测试SCP下载功能")
    print("=" * 40)
    
    device_ip = input("请输入ALE设备IP: ").strip()
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()
    
    if not all([device_ip, username, password]):
        print("请输入完整信息")
        return
    
    try:
        import paramiko
        
        print(f"尝试连接: {device_ip}")
        
        # 创建SSH连接
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(device_ip, username=username, password=password, timeout=10)
        
        print("✓ SSH连接成功")
        
        # 创建SFTP客户端
        sftp = ssh.open_sftp()
        
        # 列出根目录文件
        print("\n根目录文件列表:")
        try:
            files = sftp.listdir('/')
            for file in files:
                if 'tech_support' in file.lower():
                    print(f"  📄 {file}")
                else:
                    print(f"     {file}")
        except Exception as e:
            print(f"列出文件失败: {e}")
        
        # 测试下载tech-support文件
        test_files = ['tech_support.log', 'tech_support_layer3.log', 'tech_support_layer2.log']
        
        for test_file in test_files:
            try:
                print(f"\n测试下载: {test_file}")
                
                # 检查文件是否存在
                try:
                    file_stat = sftp.stat(f'/{test_file}')
                    print(f"  文件大小: {file_stat.st_size} 字节")
                    print(f"  修改时间: {datetime.fromtimestamp(file_stat.st_mtime)}")
                    
                    # 尝试下载
                    local_file = f"test_{device_ip}_{test_file}"
                    sftp.get(f'/{test_file}', local_file)
                    print(f"  ✓ 下载成功: {local_file}")
                    
                    # 显示文件前几行
                    with open(local_file, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()[:5]
                        print("  文件内容预览:")
                        for line in lines:
                            print(f"    {line.strip()}")
                    
                except FileNotFoundError:
                    print(f"  ✗ 文件不存在: {test_file}")
                except Exception as e:
                    print(f"  ✗ 下载失败: {e}")
                    
            except Exception as e:
                print(f"  ✗ 处理文件失败: {e}")
        
        sftp.close()
        ssh.close()
        
        print("\n✓ SCP测试完成")
        
    except Exception as e:
        print(f"✗ SCP测试失败: {e}")

def test_ftp_download():
    """测试FTP下载功能"""
    print("测试FTP下载功能")
    print("=" * 40)
    
    device_ip = input("请输入ALE设备IP: ").strip()
    username = input("请输入FTP用户名: ").strip()
    password = input("请输入FTP密码: ").strip()
    
    if not all([device_ip, username, password]):
        print("请输入完整信息")
        return
    
    try:
        import ftplib
        
        print(f"尝试FTP连接: {device_ip}")
        
        with ftplib.FTP(device_ip) as ftp:
            ftp.login(username, password)
            print("✓ FTP连接成功")
            
            # 列出根目录文件
            print("\n根目录文件列表:")
            files = ftp.nlst()
            for file in files:
                if 'tech_support' in file.lower():
                    print(f"  📄 {file}")
                else:
                    print(f"     {file}")
            
            # 测试下载
            test_files = ['tech_support.log', 'tech_support_layer3.log', 'tech_support_layer2.log']
            
            for test_file in test_files:
                try:
                    print(f"\n测试下载: {test_file}")
                    local_file = f"test_ftp_{device_ip}_{test_file}"
                    
                    with open(local_file, 'wb') as f:
                        ftp.retrbinary(f'RETR {test_file}', f.write)
                    
                    print(f"  ✓ 下载成功: {local_file}")
                    
                    # 显示文件大小
                    size = os.path.getsize(local_file)
                    print(f"  文件大小: {size} 字节")
                    
                except Exception as e:
                    print(f"  ✗ 下载失败: {e}")
        
        print("\n✓ FTP测试完成")
        
    except Exception as e:
        print(f"✗ FTP测试失败: {e}")

def test_ale_inspection():
    """测试ALE巡检程序"""
    print("测试ALE巡检程序")
    print("=" * 40)
    
    try:
        from ale_inspection import ALEInspection
        
        # 创建巡检实例
        inspector = ALEInspection()
        
        # 获取设备信息
        devices = list(inspector.get_device_info())
        
        if not devices:
            print("没有找到设备配置")
            return
        
        print(f"找到 {len(devices)} 个设备")
        
        # 找到ALE设备
        ale_devices = []
        for device in devices:
            device_type = device.get('device_type', '').lower()
            if 'alcatel' in device_type or 'ale' in device_type:
                ale_devices.append(device)
        
        if not ale_devices:
            print("没有找到ALE设备")
            return
        
        print(f"找到 {len(ale_devices)} 个ALE设备")
        
        # 选择一个设备进行测试
        test_device = ale_devices[0]
        device_ip = test_device['ip']
        
        print(f"测试设备: {device_ip}")
        
        # 测试连接
        connection = inspector.connect_device(test_device)
        if connection:
            print("✓ 设备连接成功")
            
            # 测试tech-support下载
            print("测试tech-support下载...")
            success = inspector.execute_ale_tech_support(
                connection, device_ip, 
                test_device['username'], test_device['password']
            )
            
            if success:
                print("✓ tech-support处理成功")
            else:
                print("✗ tech-support处理失败")
            
            connection.disconnect()
        else:
            print("✗ 设备连接失败")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def main():
    """主函数"""
    print("ALE设备下载功能测试工具")
    print("=" * 50)
    
    while True:
        print("\n选择测试:")
        print("1. 测试SCP下载")
        print("2. 测试FTP下载")
        print("3. 测试ALE巡检程序")
        print("4. 配置认证信息")
        print("0. 退出")
        
        choice = input("\n请选择: ").strip()
        
        if choice == '1':
            test_scp_download()
        elif choice == '2':
            test_ftp_download()
        elif choice == '3':
            test_ale_inspection()
        elif choice == '4':
            try:
                from ale_config import main as config_main
                config_main()
            except ImportError:
                print("ale_config.py不可用")
        elif choice == '0':
            break
        else:
            print("无效选择")

if __name__ == '__main__':
    main()
