#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
安装GUI版本所需的依赖
"""

import subprocess
import sys

def install_requirements():
    """安装GUI所需的依赖"""
    print("安装GUI版本依赖...")
    
    # 基础依赖
    base_requirements = [
        "netmiko>=4.0.0",
        "paramiko>=2.7.0", 
        "openpyxl>=3.0.0",
        "pandas>=1.3.0"
    ]
    
    # GUI打包依赖
    gui_requirements = [
        "pyinstaller>=5.0.0",
        "auto-py-to-exe"  # 可选：提供GUI界面配置打包
    ]
    
    all_requirements = base_requirements + gui_requirements
    
    for req in all_requirements:
        try:
            print(f"安装 {req}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", req])
            print(f"✓ {req} 安装成功")
        except subprocess.CalledProcessError:
            print(f"✗ {req} 安装失败")
    
    print("\n依赖安装完成！")
    print("现在可以运行:")
    print("1. python ale_gui.py  # 运行GUI版本")
    print("2. python build_exe.py  # 打包成exe")

if __name__ == '__main__':
    install_requirements()
