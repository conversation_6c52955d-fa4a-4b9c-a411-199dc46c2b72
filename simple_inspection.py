#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
简化版网络设备巡检程序
整合现有模块，支持SSH用户名密码配置和多厂商设备
"""

import os
import sys
import time
import ftplib
from datetime import datetime

def check_dependencies():
    """检查依赖模块"""
    missing_modules = []
    
    try:
        import netmiko
    except ImportError:
        missing_modules.append("netmiko")
    
    try:
        import openpyxl
    except ImportError:
        missing_modules.append("openpyxl")
    
    if missing_modules:
        print("缺少以下依赖模块:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请运行以下命令安装:")
        print("pip install " + " ".join(missing_modules))
        return False
    
    return True

def check_excel_file():
    """检查现有Excel文件"""
    excel_file = "template.xlsx"
    if not os.path.exists(excel_file):
        print(f"Excel配置文件不存在: {excel_file}")
        print("请确保Excel配置文件存在并包含设备信息")
        return False
    print(f"✓ 找到Excel配置文件: {excel_file}")
    return True

def run_connection_test():
    """运行连接测试"""
    try:
        from connect import BackupConfig
        
        print("=" * 60)
        print("开始设备连接测试")
        print("=" * 60)
        
        backup = BackupConfig()
        backup.connect_t()
        
        print("\n连接测试结果:")
        print(f"成功设备: {len(backup.success)}")
        print(f"失败设备: {len(backup.fail)}")
        
        if backup.success:
            print("\n成功设备列表:")
            for device in backup.success:
                print(f"  ✓ {device}")
        
        if backup.fail:
            print("\n失败设备列表:")
            for device in backup.fail:
                print(f"  ✗ {device}")
        
        return True
        
    except Exception as e:
        print(f"连接测试失败: {e}")
        return False

def download_ale_logs(device_ip, connection):
    """下载ALE设备的tech-support日志文件"""
    try:
        print(f"开始为ALE设备 {device_ip} 处理tech-support...")

        # 1. 执行tech-support命令
        print(f"执行tech-support命令: {device_ip}")
        output = connection.send_command("show tech-support", delay_factor=3)
        print(f"tech-support命令执行完成: {device_ip}")

        # 2. 等待文件生成
        print(f"等待日志文件生成: {device_ip}")
        time.sleep(15)  # 给设备更多时间生成文件

        # 3. 创建设备专用目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        device_log_dir = os.path.join("LOG", f"{device_ip}_{timestamp}")
        if not os.path.exists(device_log_dir):
            os.makedirs(device_log_dir)
            print(f"创建目录: {device_log_dir}")

        # 4. 尝试使用TFTP下载日志文件
        try:
            from tftp_downloader import download_ale_tech_support_logs
            print(f"尝试TFTP下载日志文件: {device_ip}")

            # 使用专用的TFTP下载器
            tftp_success = download_ale_tech_support_logs(device_ip, "LOG")

            if tftp_success:
                print(f"✓ TFTP下载成功: {device_ip}")
                return True
            else:
                print(f"! TFTP下载失败，创建备用记录: {device_ip}")

        except ImportError:
            print(f"! TFTP模块不可用，创建备用记录: {device_ip}")
        except Exception as e:
            print(f"! TFTP下载异常: {device_ip} - {e}")

        # 5. 如果TFTP失败，创建备用记录文件
        log_files = [
            "tech_support_layer3.log",
            "tech_support_layer2.log",
            "tech_support.log"
        ]

        backup_files = []
        for log_file in log_files:
            local_filename = f"{device_ip}_{log_file}"
            local_path = os.path.join(device_log_dir, local_filename)

            with open(local_path, 'w', encoding='utf-8') as f:
                f.write(f"ALE设备tech-support日志记录\n")
                f.write(f"设备IP: {device_ip}\n")
                f.write(f"原始文件名: {log_file}\n")
                f.write(f"生成时间: {datetime.now()}\n")
                f.write("=" * 50 + "\n")
                f.write("说明: TFTP下载失败，这是备用记录文件\n")
                f.write("请手动从设备根目录获取以下文件:\n")
                f.write("- tech_support_layer3.log\n")
                f.write("- tech_support_layer2.log\n")
                f.write("- tech_support.log\n")
                f.write("=" * 50 + "\n")
                f.write(f"tech-support命令输出:\n{output}\n")

            backup_files.append(local_filename)
            print(f"创建备用记录: {local_path}")

        print(f"ALE设备 {device_ip} 处理完成: {len(backup_files)} 个记录文件")
        return True

    except Exception as e:
        print(f"ALE设备处理失败 {device_ip}: {e}")
        return False

def run_device_inspection():
    """运行设备信息采集（包含ALE特殊处理）"""
    try:
        from connect import BackupConfig
        from netmiko import ConnectHandler

        print("=" * 60)
        print("开始设备信息采集（支持ALE特殊处理）")
        print("=" * 60)

        backup = BackupConfig()

        # 获取设备信息
        devices = list(backup.get_device_info())
        if not devices:
            print("没有找到设备配置")
            return False

        print(f"发现 {len(devices)} 个设备")

        # 创建LOG目录
        if not os.path.exists("LOG"):
            os.makedirs("LOG")
            print("创建LOG目录")

        # 处理每个设备
        for host in devices:
            device_ip = host['ip']
            device_type = host['device_type'].lower()

            print(f"\n处理设备: {device_ip} ({device_type})")

            try:
                # 连接设备
                connect_params = {
                    'device_type': host['device_type'],
                    'host': host['ip'],
                    'username': host['username'],
                    'password': host['password'],
                    'port': host['port'] if host['port'] else (22 if host['protocol'] == 'ssh' else 23)
                }

                if host['secret']:
                    connect_params['secret'] = host['secret']

                connection = ConnectHandler(**connect_params)

                # 如果是ALE设备，执行特殊处理
                if 'alcatel' in device_type or 'ale' in device_type:
                    print(f"检测到ALE设备，执行特殊处理: {device_ip}")
                    download_ale_logs(device_ip, connection)

                # 执行常规命令
                if host['cmd_list']:
                    print(f"执行常规巡检命令: {device_ip}")
                    for cmd in host['cmd_list']:
                        try:
                            output = connection.send_command(cmd)
                            print(f"命令执行完成: {device_ip} - {cmd}")
                        except Exception as e:
                            print(f"命令执行失败: {device_ip} - {cmd} - {e}")

                connection.disconnect()
                backup.success.append(device_ip)
                print(f"设备处理完成: {device_ip}")

            except Exception as e:
                print(f"设备处理失败: {device_ip} - {e}")
                backup.fail.append(device_ip)

        print("\n信息采集结果:")
        print(f"成功设备: {len(backup.success)}")
        print(f"失败设备: {len(backup.fail)}")

        if backup.success:
            print("\n成功设备列表:")
            for device in backup.success:
                print(f"  ✓ {device}")

        if backup.fail:
            print("\n失败设备列表:")
            for device in backup.fail:
                print(f"  ✗ {device}")

        return True

    except Exception as e:
        print(f"设备信息采集失败: {e}")
        return False

def compress_results():
    """压缩巡检结果"""
    try:
        from zip_file import compress_zip
        
        log_dir = "LOG"
        if not os.path.exists(log_dir):
            print("LOG目录不存在，无需压缩")
            return False
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"inspection_results_{timestamp}.zip"
        
        if compress_zip(log_dir, zip_filename):
            print(f"✓ 巡检结果已压缩: {zip_filename}")
            return True
        else:
            print("✗ 压缩失败")
            return False
            
    except Exception as e:
        print(f"压缩失败: {e}")
        return False

def send_email_notification():
    """发送邮件通知"""
    try:
        from send_email import send
        
        print("准备发送邮件通知...")
        print("注意: 请先在send_email.py中配置邮件参数")
        
        choice = input("是否发送邮件? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            # send()  # 取消注释并配置邮件参数后使用
            print("邮件发送功能需要配置后启用")
        
        return True
        
    except Exception as e:
        print(f"邮件发送失败: {e}")
        return False

def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    网络设备自动化巡检系统                      ║
║                  Network Device Inspection System            ║
╠══════════════════════════════════════════════════════════════╣
║  功能：                                                      ║
║  1. 批量连接测试                                             ║
║  2. 设备信息采集                                             ║
║  3. 完整巡检流程                                             ║
║  4. 结果打包压缩                                             ║
║  5. 邮件发送通知                                             ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def run_full_inspection():
    """运行完整巡检流程（按照ALE巡检要求）"""
    print("开始完整巡检流程...")
    print("流程说明:")
    print("1. 直接使用现有Excel配置")
    print("2. ALE设备执行tech-support并下载日志文件")
    print("3. 所有设备完成后压缩LOG文件夹")
    print("4. 发送邮件通知")

    # 1. 设备信息采集（包含ALE特殊处理）
    print("\n步骤 1/3: 设备巡检（包含ALE特殊处理）")
    success = run_device_inspection()

    if not success:
        print("巡检失败，终止流程")
        return

    # 2. 压缩LOG文件夹
    print("\n步骤 2/3: 压缩LOG文件夹")
    compress_success = compress_results()

    # 3. 发送邮件通知
    print("\n步骤 3/3: 发送邮件通知")
    send_email_notification()

    print("\n" + "=" * 60)
    print("完整巡检流程完成!")
    if compress_success:
        print("✓ LOG文件夹已压缩")
    print("✓ 邮件通知已处理")
    print("=" * 60)

def interactive_mode():
    """交互式模式"""
    while True:
        print("\n请选择操作:")
        print("1. 设备连接测试")
        print("2. 设备信息采集")
        print("3. 完整巡检流程")
        print("4. 压缩现有结果")
        print("5. 发送邮件通知")
        print("0. 退出程序")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '1':
            run_connection_test()
        elif choice == '2':
            run_device_inspection()
        elif choice == '3':
            run_full_inspection()
        elif choice == '4':
            compress_results()
        elif choice == '5':
            send_email_notification()
        elif choice == '0':
            print("程序退出")
            break
        else:
            print("无效选择，请重新输入")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查Excel配置文件
    if not check_excel_file():
        return
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--auto':
            # 自动模式：执行完整巡检流程
            run_full_inspection()
        elif sys.argv[1] == '--test':
            # 测试模式：仅执行连接测试
            run_connection_test()
        elif sys.argv[1] == '--collect':
            # 采集模式：仅执行信息采集
            run_device_inspection()
        else:
            print("无效参数。支持的参数:")
            print("  --auto    : 执行完整巡检流程")
            print("  --test    : 仅执行连接测试")
            print("  --collect : 仅执行信息采集")
            print("  无参数    : 进入交互式模式")
    else:
        # 交互式模式
        interactive_mode()

if __name__ == '__main__':
    main()
