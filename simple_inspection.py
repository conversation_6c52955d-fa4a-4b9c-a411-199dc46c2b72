#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
简化版网络设备巡检程序
整合现有模块，支持SSH用户名密码配置和多厂商设备
"""

import os
import sys
from datetime import datetime

def check_dependencies():
    """检查依赖模块"""
    missing_modules = []
    
    try:
        import netmiko
    except ImportError:
        missing_modules.append("netmiko")
    
    try:
        import openpyxl
    except ImportError:
        missing_modules.append("openpyxl")
    
    if missing_modules:
        print("缺少以下依赖模块:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请运行以下命令安装:")
        print("pip install " + " ".join(missing_modules))
        return False
    
    return True

def check_template_file():
    """检查模板文件"""
    template_file = "template.xlsx"
    if not os.path.exists(template_file):
        print(f"模板文件不存在: {template_file}")
        print("请运行以下命令创建模板:")
        print("python create_simple_template.py")
        return False
    return True

def run_connection_test():
    """运行连接测试"""
    try:
        from connect import BackupConfig
        
        print("=" * 60)
        print("开始设备连接测试")
        print("=" * 60)
        
        backup = BackupConfig()
        backup.connect_t()
        
        print("\n连接测试结果:")
        print(f"成功设备: {len(backup.success)}")
        print(f"失败设备: {len(backup.fail)}")
        
        if backup.success:
            print("\n成功设备列表:")
            for device in backup.success:
                print(f"  ✓ {device}")
        
        if backup.fail:
            print("\n失败设备列表:")
            for device in backup.fail:
                print(f"  ✗ {device}")
        
        return True
        
    except Exception as e:
        print(f"连接测试失败: {e}")
        return False

def run_device_inspection():
    """运行设备信息采集"""
    try:
        from connect import BackupConfig
        
        print("=" * 60)
        print("开始设备信息采集")
        print("=" * 60)
        
        backup = BackupConfig()
        backup.connect()
        
        print("\n信息采集结果:")
        print(f"成功设备: {len(backup.success)}")
        print(f"失败设备: {len(backup.fail)}")
        
        if backup.success:
            print("\n成功设备列表:")
            for device in backup.success:
                print(f"  ✓ {device}")
        
        if backup.fail:
            print("\n失败设备列表:")
            for device in backup.fail:
                print(f"  ✗ {device}")
        
        return True
        
    except Exception as e:
        print(f"设备信息采集失败: {e}")
        return False

def compress_results():
    """压缩巡检结果"""
    try:
        from zip_file import compress_zip
        
        log_dir = "LOG"
        if not os.path.exists(log_dir):
            print("LOG目录不存在，无需压缩")
            return False
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"inspection_results_{timestamp}.zip"
        
        if compress_zip(log_dir, zip_filename):
            print(f"✓ 巡检结果已压缩: {zip_filename}")
            return True
        else:
            print("✗ 压缩失败")
            return False
            
    except Exception as e:
        print(f"压缩失败: {e}")
        return False

def send_email_notification():
    """发送邮件通知"""
    try:
        from send_email import send
        
        print("准备发送邮件通知...")
        print("注意: 请先在send_email.py中配置邮件参数")
        
        choice = input("是否发送邮件? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            # send()  # 取消注释并配置邮件参数后使用
            print("邮件发送功能需要配置后启用")
        
        return True
        
    except Exception as e:
        print(f"邮件发送失败: {e}")
        return False

def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    网络设备自动化巡检系统                      ║
║                  Network Device Inspection System            ║
╠══════════════════════════════════════════════════════════════╣
║  功能：                                                      ║
║  1. 批量连接测试                                             ║
║  2. 设备信息采集                                             ║
║  3. 完整巡检流程                                             ║
║  4. 结果打包压缩                                             ║
║  5. 邮件发送通知                                             ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def run_full_inspection():
    """运行完整巡检流程"""
    print("开始完整巡检流程...")
    
    # 1. 连接测试
    print("\n步骤 1/4: 设备连接测试")
    run_connection_test()
    
    # 2. 设备信息采集
    print("\n步骤 2/4: 设备信息采集")
    run_device_inspection()
    
    # 3. 压缩结果
    print("\n步骤 3/4: 压缩巡检结果")
    compress_results()
    
    # 4. 发送通知
    print("\n步骤 4/4: 发送邮件通知")
    send_email_notification()
    
    print("\n" + "=" * 60)
    print("完整巡检流程完成!")
    print("=" * 60)

def interactive_mode():
    """交互式模式"""
    while True:
        print("\n请选择操作:")
        print("1. 设备连接测试")
        print("2. 设备信息采集")
        print("3. 完整巡检流程")
        print("4. 压缩现有结果")
        print("5. 发送邮件通知")
        print("0. 退出程序")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '1':
            run_connection_test()
        elif choice == '2':
            run_device_inspection()
        elif choice == '3':
            run_full_inspection()
        elif choice == '4':
            compress_results()
        elif choice == '5':
            send_email_notification()
        elif choice == '0':
            print("程序退出")
            break
        else:
            print("无效选择，请重新输入")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查模板文件
    if not check_template_file():
        return
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--auto':
            # 自动模式：执行完整巡检流程
            run_full_inspection()
        elif sys.argv[1] == '--test':
            # 测试模式：仅执行连接测试
            run_connection_test()
        elif sys.argv[1] == '--collect':
            # 采集模式：仅执行信息采集
            run_device_inspection()
        else:
            print("无效参数。支持的参数:")
            print("  --auto    : 执行完整巡检流程")
            print("  --test    : 仅执行连接测试")
            print("  --collect : 仅执行信息采集")
            print("  无参数    : 进入交互式模式")
    else:
        # 交互式模式
        interactive_mode()

if __name__ == '__main__':
    main()
