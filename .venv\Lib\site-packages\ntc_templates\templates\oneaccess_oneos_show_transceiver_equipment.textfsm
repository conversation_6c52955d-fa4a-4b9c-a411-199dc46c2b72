Value Filldown PORT (\S+)
Value Required PHYSICAL_DEVICE (.*\S)
Value CONNECTOR (\S+)
Value VENDOR_NAME (.*\S)
Value VENDOR_ID (.*\S)
Value VENDOR_PARTNER (.*\S)
Value VENDOR_REVISION (.*\S)
Value VENDOR_DATECODE (.*\S)
Value TCVR_ETHER_COMPLIANCE (.*\S)
Value TCVR_10G_ETHER_COMPLIANCE (.*\S)
Value TCVR_FIBER_LINKLEN (.*\S)
Value TCVR_FIBER_TECH (.*\S)
Value TCVR_FIBER_TX_MEDIA (.*\S)
Value TCVR_FIBER_SPEED (.*\S)
Value WAVELENGTH (.*\S)
Value ENCODING (.*\S)
Value NOMINAL_BITRATE (.*\S)
Value LINKLEN_SM (.*\S)
Value LINKLEN_50_MM (.*\S)
Value LINKLEN_625_MM (.*\S)
Value LINKLEN_COPPER (.*\S)
Value List OPTIONS (\S.*\S)
Value MAX_BIT_RATE (\S.*\S)
Value MIN_BIT_RATE (\S.*\S)
Value TEMP_HIGH_ALARM (\S.*\S)
Value TEMP_LOW_ALARM (\S.*\S)
Value TEMP_HIGH_WARNING (\S.*\S)
Value TEMP_LOW_WARNING (\S.*\S)
Value VOLTAGE_HIGH_ALARM (\S.*\S)
Value VOLTAGE_LOW_ALARM (\S.*\S)
Value VOLTAGE_HIGH_WARNING (\S.*\S)
Value VOLTAGE_LOW_WARNING (\S.*\S)
Value BIAS_HIGH_ALARM (\S.*\S)
Value BIAS_LOW_ALARM (\S.*\S)
Value BIAS_HIGH_WARNING (\S.*\S)
Value BIAS_LOW_WARNING (\S.*\S)
Value TX_POWER_HIGH_ALARM (\S.*\S)
Value TX_POWER_LOW_ALARM (\S.*\S)
Value TX_POWER_HIGH_WARNING (\S.*\S)
Value TX_POWER_LOW_WARNING (\S.*\S)
Value RX_POWER_HIGH_ALARM (\S.*\S)
Value RX_POWER_LOW_ALARM (\S.*\S)
Value RX_POWER_HIGH_WARNING (\S.*\S)
Value RX_POWER_LOW_WARNING (\S.*\S)
Value MEASURED_MODULE_TEMP (\S.*\S)
Value MEASURED_SUPPLY_VOLTAGE (\S.*\S)
Value MEASURED_TX_BIAS_CURRENT (\S.*\S)
Value MEASURED_TX_OUTPUT_POWER (\S.*\S)
Value MEASURED_RX_INPUT_POWER (\S.*\S)

Start
  ^SFP -> Continue.Record
  ^SFP\s${PORT}:
  ^\s*No\s+(Inventory\s+data\s+available|SFP\s+module\s+present)
  ^\s*Physical\sdevice\s+=\s+${PHYSICAL_DEVICE}
  ^\s*connector\s+=\s+${CONNECTOR}
  ^\s*vendor: -> VENDOR
  ^\s*wavelength\s+=\s+${WAVELENGTH}
  ^\s*encoding\s+=\s+${ENCODING}
  ^\s*nominalBitRate\s+=\s+${NOMINAL_BITRATE}
  ^\s*Link\slength\sin: -> LINK_LENGTH
  ^\s*[mM]in\sbit\srate\s+=\s+${MIN_BIT_RATE}
  ^\s*Diagnostics\scalibration\sis\sinternal -> DIAGNOSTICS
  ^\s*SFP\s+module\s+inventory\s+information\s*:
  ^\s*$$
  ^. -> Error

VENDOR
  ^\s*name\s+=\s+${VENDOR_NAME}
  ^\s*id\s+=\s+${VENDOR_ID}
  ^\s*partNumber\s+=\s+${VENDOR_PARTNER}
  ^\s*revision\s+=\s+${VENDOR_REVISION}
  ^\s*dateCode\s+=\s+${VENDOR_DATECODE}
  ^\s*transceiver: -> TRANSCEIVER
  ^\s+serialNumber\s+=
  ^\s*$$
  ^. -> Error

TRANSCEIVER
  ^\s*ethernetComplianceCode\s+=\s+${TCVR_ETHER_COMPLIANCE}
  ^\s*10G\sethernetComplianceCode\s+=\s+${TCVR_10G_ETHER_COMPLIANCE}
  ^\s*fiberLinkLen\s+=\s+${TCVR_FIBER_LINKLEN}
  ^\s*fiberTech\s+=\s+${TCVR_FIBER_TECH}
  ^\s*fiberTxMedia\s+=\s+${TCVR_FIBER_TX_MEDIA}
  ^\s*fiberSpeed\s+=\s+${TCVR_FIBER_SPEED} -> Continue
  ^\s*fiberSpeed -> Start
  ^\s+sonet
  ^\s*$$
  ^. -> Error

LINK_LENGTH
  ^\s*single\sfiber\smode\s+=\s+${LINKLEN_SM}
  ^\s*50u\smulti-mode\sfiber\s+=\s+${LINKLEN_50_MM}
  ^\s*62\.5u\smulti-mode\sfiber\s+=\s+${LINKLEN_625_MM}
  ^\s*copper\scable\s+=\s+${LINKLEN_COPPER} -> Continue
  ^\s*copper\scable -> OPTIONS
  ^\s*$$
  ^. -> Error

OPTIONS
  ^\s*Max\sbit\srate\s+=\s+${MAX_BIT_RATE} -> Start
  ^\s*options\s+=\s${OPTIONS}
  ^\s+${OPTIONS}
  ^\s*$$
  ^. -> Error

DIAGNOSTICS
  ^\s*Temp\sHigh\sAlarm\s+=\s+${TEMP_HIGH_ALARM}
  ^\s*Temp\sLow\sAlarm\s+=\s+${TEMP_LOW_ALARM}
  ^\s*Temp\sHigh\sWarning\s+=\s+${TEMP_HIGH_WARNING}
  ^\s*Temp\sLow\sWarning\s+=\s+${TEMP_LOW_WARNING}
  ^\s*Voltage\sHigh\sAlarm\s+=\s+${VOLTAGE_HIGH_ALARM}
  ^\s*Voltage\sLow\sAlarm\s+=\s+${VOLTAGE_LOW_ALARM}
  ^\s*Voltage\sHigh\sWarning\s+=\s+${VOLTAGE_HIGH_WARNING}
  ^\s*Voltage\sLow\sWarning\s+=\s+${VOLTAGE_LOW_WARNING}
  ^\s*Bias\sHigh\sAlarm\s+=\s+${BIAS_HIGH_ALARM}
  ^\s*Bias\sLow\sAlarm\s+=\s+${BIAS_LOW_ALARM}
  ^\s*Bias\sHigh\sWarning\s+=\s+${BIAS_HIGH_WARNING}
  ^\s*Bias\sLow\sWarning\s+=\s+${BIAS_LOW_WARNING}
  ^\s*TX\sPower\sHigh\sAlarm\s+=\s+${TX_POWER_HIGH_ALARM}
  ^\s*TX\sPower\sLow\sAlarm\s+=\s+${TX_POWER_LOW_ALARM}
  ^\s*TX\sPower\sHigh\sWarning\s+=\s+${TX_POWER_HIGH_WARNING}
  ^\s*TX\sPower\sLow\sWarning\s+=\s+${TX_POWER_LOW_WARNING}
  ^\s*RX\sPower\sHigh\sAlarm\s+=\s+${RX_POWER_HIGH_ALARM}
  ^\s*RX\sPower\sLow\sAlarm\s+=\s+${RX_POWER_LOW_ALARM}
  ^\s*RX\sPower\sHigh\sWarning\s+=\s+${RX_POWER_HIGH_WARNING}
  ^\s*RX\sPower\sLow\sWarning\s+=\s+${RX_POWER_LOW_WARNING}
  ^\s*Internally\sMeasured\sModule\sTemperature\s+=\s+${MEASURED_MODULE_TEMP}
  ^\s*Internally\sMeasured\sSupply\sVoltage\s+=\s+${MEASURED_SUPPLY_VOLTAGE}
  ^\s*Internally\sMeasured\sTx\sBias\sCurrent\s+=\s+${MEASURED_TX_BIAS_CURRENT}
  ^\s*Measured\sTx\sOutput\sPower\s+=\s+${MEASURED_TX_OUTPUT_POWER}
  ^\s*Measured\sRx\sInput\sPower\s+=\s+${MEASURED_RX_INPUT_POWER}
  ^\s*Optional\s+Status
  ^\s*Alarm
  ^SFP -> Continue.Record
  ^SFP\s+${PORT}: -> Start
  ^\s*$$
  ^. -> Error
