#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
ALE网络运维工具包 - GUI界面
使用tkinter创建现代化界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from datetime import datetime
import queue

# 导入核心功能
try:
    from ale_inspection import ALEInspection
    from env_loader import get_email_config, validate_email_config
except ImportError as e:
    print(f"导入模块失败: {e}")

class ModernStyle:
    """现代化样式配置"""
    
    # 颜色主题
    PRIMARY_COLOR = "#2E86AB"      # 主色调 - 蓝色
    SECONDARY_COLOR = "#A23B72"    # 次色调 - 紫色  
    SUCCESS_COLOR = "#F18F01"      # 成功色 - 橙色
    ERROR_COLOR = "#C73E1D"        # 错误色 - 红色
    BACKGROUND_COLOR = "#F5F5F5"   # 背景色 - 浅灰
    CARD_COLOR = "#FFFFFF"         # 卡片色 - 白色
    TEXT_COLOR = "#2C3E50"         # 文字色 - 深灰
    
    # 字体配置
    TITLE_FONT = ("Microsoft YaHei UI", 16, "bold")
    HEADING_FONT = ("Microsoft YaHei UI", 12, "bold") 
    BODY_FONT = ("Microsoft YaHei UI", 10)
    BUTTON_FONT = ("Microsoft YaHei UI", 10, "bold")

class ALEToolkitGUI:
    """ALE网络运维工具包GUI主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        self.message_queue = queue.Queue()
        self.check_queue()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("ALE网络运维工具包 v2.0")
        self.root.geometry("1000x700")
        self.root.configure(bg=ModernStyle.BACKGROUND_COLOR)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 居中显示
        self.center_window()
        
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        
        # 配置按钮样式
        style.configure(
            "Primary.TButton",
            font=ModernStyle.BUTTON_FONT,
            padding=(20, 10)
        )
        
        style.configure(
            "Secondary.TButton", 
            font=ModernStyle.BUTTON_FONT,
            padding=(15, 8)
        )
        
        # 配置标签样式
        style.configure(
            "Title.TLabel",
            font=ModernStyle.TITLE_FONT,
            background=ModernStyle.BACKGROUND_COLOR,
            foreground=ModernStyle.PRIMARY_COLOR
        )
        
        style.configure(
            "Heading.TLabel",
            font=ModernStyle.HEADING_FONT,
            background=ModernStyle.CARD_COLOR,
            foreground=ModernStyle.TEXT_COLOR
        )
        
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = tk.Frame(self.root, bg=ModernStyle.BACKGROUND_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题区域
        self.create_header(main_frame)
        
        # 内容区域
        content_frame = tk.Frame(main_frame, bg=ModernStyle.BACKGROUND_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # 左侧控制面板
        self.create_control_panel(content_frame)
        
        # 右侧日志面板
        self.create_log_panel(content_frame)
        
        # 底部状态栏
        self.create_status_bar(main_frame)
        
    def create_header(self, parent):
        """创建标题区域"""
        header_frame = tk.Frame(parent, bg=ModernStyle.CARD_COLOR, relief=tk.RAISED, bd=1)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 标题
        title_label = ttk.Label(
            header_frame,
            text="🔧 ALE网络运维工具包",
            style="Title.TLabel"
        )
        title_label.pack(pady=15)
        
        # 副标题
        subtitle_label = tk.Label(
            header_frame,
            text="自动化网络设备运维 | 支持ALE及多厂商设备",
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.CARD_COLOR,
            fg=ModernStyle.TEXT_COLOR
        )
        subtitle_label.pack(pady=(0, 15))
        
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_frame = tk.Frame(parent, bg=ModernStyle.CARD_COLOR, relief=tk.RAISED, bd=1)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.configure(width=350)
        control_frame.pack_propagate(False)
        
        # 面板标题
        ttk.Label(
            control_frame,
            text="📋 运维控制面板",
            style="Heading.TLabel"
        ).pack(pady=15)
        
        # 配置文件选择
        self.create_file_selection(control_frame)
        
        # 邮件配置检查
        self.create_email_config(control_frame)
        
        # 运维选项
        self.create_operation_options(control_frame)
        
        # 操作按钮
        self.create_action_buttons(control_frame)
        
    def create_file_selection(self, parent):
        """创建文件选择区域"""
        file_frame = tk.LabelFrame(
            parent,
            text="📁 配置文件",
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.CARD_COLOR,
            fg=ModernStyle.TEXT_COLOR
        )
        file_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # Excel文件选择
        excel_frame = tk.Frame(file_frame, bg=ModernStyle.CARD_COLOR)
        excel_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(
            excel_frame,
            text="设备配置文件:",
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.CARD_COLOR
        ).pack(anchor=tk.W)
        
        self.excel_path = tk.StringVar(value="template.xlsx")
        excel_entry = tk.Entry(
            excel_frame,
            textvariable=self.excel_path,
            font=ModernStyle.BODY_FONT,
            width=30
        )
        excel_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, pady=2)
        
        ttk.Button(
            excel_frame,
            text="浏览",
            command=self.browse_excel_file,
            style="Secondary.TButton"
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
    def create_email_config(self, parent):
        """创建邮件配置区域"""
        email_frame = tk.LabelFrame(
            parent,
            text="📧 邮件配置",
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.CARD_COLOR,
            fg=ModernStyle.TEXT_COLOR
        )
        email_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # 配置状态显示
        self.email_status = tk.Label(
            email_frame,
            text="检查邮件配置...",
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.CARD_COLOR,
            fg=ModernStyle.TEXT_COLOR
        )
        self.email_status.pack(pady=5)
        
        # 检查按钮
        ttk.Button(
            email_frame,
            text="检查配置",
            command=self.check_email_config,
            style="Secondary.TButton"
        ).pack(pady=5)
        
        # 初始检查
        self.check_email_config()
        
    def create_operation_options(self, parent):
        """创建运维选项"""
        options_frame = tk.LabelFrame(
            parent,
            text="⚙️ 运维选项",
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.CARD_COLOR,
            fg=ModernStyle.TEXT_COLOR
        )
        options_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # 选项变量
        self.auto_email = tk.BooleanVar(value=True)
        self.auto_compress = tk.BooleanVar(value=True)
        self.verbose_log = tk.BooleanVar(value=True)
        
        # 选项复选框
        tk.Checkbutton(
            options_frame,
            text="自动发送邮件",
            variable=self.auto_email,
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.CARD_COLOR
        ).pack(anchor=tk.W, pady=2)
        
        tk.Checkbutton(
            options_frame,
            text="自动压缩结果",
            variable=self.auto_compress,
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.CARD_COLOR
        ).pack(anchor=tk.W, pady=2)
        
        tk.Checkbutton(
            options_frame,
            text="详细日志输出",
            variable=self.verbose_log,
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.CARD_COLOR
        ).pack(anchor=tk.W, pady=2)
        
    def create_action_buttons(self, parent):
        """创建操作按钮"""
        button_frame = tk.Frame(parent, bg=ModernStyle.CARD_COLOR)
        button_frame.pack(fill=tk.X, padx=15, pady=20)
        
        # 主要操作按钮
        self.start_button = ttk.Button(
            button_frame,
            text="🚀 开始运维",
            command=self.start_inspection,
            style="Primary.TButton"
        )
        self.start_button.pack(fill=tk.X, pady=5)
        
        # 次要操作按钮
        ttk.Button(
            button_frame,
            text="📊 查看结果",
            command=self.view_results,
            style="Secondary.TButton"
        ).pack(fill=tk.X, pady=2)
        
        ttk.Button(
            button_frame,
            text="🧹 清理日志",
            command=self.clean_logs,
            style="Secondary.TButton"
        ).pack(fill=tk.X, pady=2)
        
    def create_log_panel(self, parent):
        """创建右侧日志面板"""
        log_frame = tk.Frame(parent, bg=ModernStyle.CARD_COLOR, relief=tk.RAISED, bd=1)
        log_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 日志标题
        ttk.Label(
            log_frame,
            text="📝 运维日志",
            style="Heading.TLabel"
        ).pack(pady=15)
        
        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            font=("Consolas", 9),
            bg="#1E1E1E",
            fg="#FFFFFF",
            insertbackground="#FFFFFF",
            wrap=tk.WORD
        )
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        # 添加欢迎信息
        self.log_message("欢迎使用ALE网络运维工具包!", "INFO")
        self.log_message("请配置设备文件并检查邮件设置后开始运维。", "INFO")
        
    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = tk.Frame(parent, bg=ModernStyle.PRIMARY_COLOR, height=30)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            status_frame,
            text="就绪",
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.PRIMARY_COLOR,
            fg="white"
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 时间显示
        self.time_label = tk.Label(
            status_frame,
            text=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            font=ModernStyle.BODY_FONT,
            bg=ModernStyle.PRIMARY_COLOR,
            fg="white"
        )
        self.time_label.pack(side=tk.RIGHT, padx=10, pady=5)
        
        # 更新时间
        self.update_time()
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 根据级别设置颜色
        colors = {
            "INFO": "#00FF00",
            "WARNING": "#FFFF00", 
            "ERROR": "#FF0000",
            "SUCCESS": "#00FFFF"
        }
        
        color = colors.get(level, "#FFFFFF")
        
        # 插入消息
        self.log_text.insert(tk.END, f"[{timestamp}] [{level}] {message}\n")
        self.log_text.see(tk.END)
        
        # 更新状态栏
        self.status_label.config(text=message[:50] + "..." if len(message) > 50 else message)
        
    def browse_excel_file(self):
        """浏览Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择设备配置文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.excel_path.set(filename)
            self.log_message(f"已选择配置文件: {os.path.basename(filename)}", "INFO")
            
    def check_email_config(self):
        """检查邮件配置"""
        try:
            config = get_email_config()
            is_valid, errors = validate_email_config()
            
            if is_valid:
                self.email_status.config(
                    text=f"✓ 邮件配置正常\n发送者: {config['sender_email']}\n接收者: {config['receiver_email']}",
                    fg=ModernStyle.SUCCESS_COLOR
                )
                self.log_message("邮件配置验证通过", "SUCCESS")
            else:
                error_text = "\n".join(errors[:2])  # 只显示前两个错误
                self.email_status.config(
                    text=f"✗ 邮件配置错误\n{error_text}",
                    fg=ModernStyle.ERROR_COLOR
                )
                self.log_message("邮件配置验证失败", "ERROR")
                
        except Exception as e:
            self.email_status.config(
                text=f"✗ 配置检查失败\n{str(e)}",
                fg=ModernStyle.ERROR_COLOR
            )
            self.log_message(f"邮件配置检查异常: {e}", "ERROR")
            
    def start_inspection(self):
        """开始运维"""
        # 检查配置文件
        if not os.path.exists(self.excel_path.get()):
            messagebox.showerror("错误", "请选择有效的设备配置文件!")
            return
            
        # 禁用开始按钮
        self.start_button.config(state="disabled", text="运维中...")
        
        # 在新线程中执行运维
        thread = threading.Thread(target=self.run_inspection)
        thread.daemon = True
        thread.start()
        
    def run_inspection(self):
        """在后台线程中运行运维"""
        try:
            self.message_queue.put(("开始ALE网络设备运维...", "INFO"))
            
            # 创建运维实例
            inspector = ALEInspection()
            
            # 重定向输出到GUI
            original_print = print
            def gui_print(*args, **kwargs):
                message = " ".join(str(arg) for arg in args)
                self.message_queue.put((message, "INFO"))
            
            # 临时替换print函数
            import builtins
            builtins.print = gui_print
            
            try:
                # 执行运维
                inspector.run_inspection()
                self.message_queue.put(("运维任务完成!", "SUCCESS"))
            finally:
                # 恢复原始print函数
                builtins.print = original_print
                
        except Exception as e:
            self.message_queue.put((f"运维过程出错: {e}", "ERROR"))
        finally:
            # 重新启用按钮
            self.message_queue.put(("ENABLE_BUTTON", "CONTROL"))
            
    def check_queue(self):
        """检查消息队列"""
        try:
            while True:
                message, level = self.message_queue.get_nowait()
                
                if level == "CONTROL":
                    if message == "ENABLE_BUTTON":
                        self.start_button.config(state="normal", text="🚀 开始运维")
                else:
                    self.log_message(message, level)
                    
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.check_queue)
            
    def view_results(self):
        """查看结果"""
        log_dir = "LOG"
        if os.path.exists(log_dir):
            os.startfile(log_dir)
            self.log_message("已打开结果目录", "INFO")
        else:
            messagebox.showinfo("提示", "还没有运维结果")
            
    def clean_logs(self):
        """清理日志"""
        if messagebox.askyesno("确认", "确定要清理所有日志文件吗？"):
            try:
                import shutil
                if os.path.exists("LOG"):
                    shutil.rmtree("LOG")
                self.log_message("日志文件已清理", "SUCCESS")
            except Exception as e:
                self.log_message(f"清理失败: {e}", "ERROR")
                
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    app = ALEToolkitGUI()
    app.run()

if __name__ == '__main__':
    main()
