
=====
BASIC
=====

#########################################
show hardware-info
#########################################
Chassis 1
CPU Manufacturer                  : Realtek
CPU Model                         : MIPS
Flash Manufacturer                : Kioxia
Flash size                        : 465641472 bytes
RAM Manufacturer                  : Winbond 
RAM size                          : 511188kB
FPGA Manufacturer                 : Lattice
FPGA version                      : 0.5
U-Boot Version                    : 5.1.8.R01
Power Supplies Present            : -
NIs Present                       : 1,-



#########################################
show chassis
#########################################
Local Chassis ID 1 (Master)
  Model Name:                    OS2260-10,
  Module Type:                   0x80062100,
  Description:                   Chassis,
  Part Number:                   904207-90,
  Hardware Revision:             01,
  Serial Number:                 WKC213100060,
  Manufacture Date:              Jul 28 2021,
  Admin Status:                  POWER ON,
  Operational Status:            UP,
  Number Of Resets:              14,
  MAC Address:                   94:24:e1:83:73:c1



#########################################
show module long
#########################################
Module in slot CMM-A
  Model Name:                    OS2260-10,
  Module Type:                   0x80062100,
  Description:                   8G 4SFP,
  Part Number:                   904207-90,
  Hardware Revision:             01,
  Serial Number:                 WKC213100060,
  Manufacture Date:              Jul 28 2021,
  FPGA - Physical 1:             0.5,
  Admin Status:                  POWER ON,
  Operational Status:            UP,
  Max Power:                     13,
  CPU Model Type:                RTL 9300,
  MAC Address:                   94:24:e1:83:73:c1,

Module in chassis 1 slot 1
  Model Name:                    OS2260-10,
  Module Type:                   0x80062100,
  Description:                   8G 4SFP,
  Part Number:                   904207-90,
  Hardware Revision:             01,
  Serial Number:                 WKC213100060,
  Manufacture Date:              Jul 28 2021,
  FPGA - Physical 1:             0.5,
  Admin Status:                  POWER ON,
  Operational Status:            UP,
  Max Power:                     13,
  CPU Model Type:                RTL 9300,
  MAC Address:                   94:24:e1:83:73:c8,
  UBOOT Version:                 5.1.8.R01



#########################################
show transceivers
#########################################
Chassis ID 1 
Slot 1 Transceiver 9
  Manufacturer Name:             CTST            ,
  Part Number:                   THCPRJ-0088-0AB ,
  Hardware Revision:             A   ,
  Serial Number:                 F220705000003   ,
  Manufacture Date:              Jul  5 2022,
  Laser Wave Length:             N/A,
  Admin Status:                  POWER ON,
  Operational Status:            UP

Slot 1 Transceiver 10
  ALU Model Name:                SFP-10G-SR     ,
  ALU Model Number:              903437-90   ,
  Hardware Revision:             1   ,
  Serial Number:                 CH07KC147       ,
  Manufacture Date:              Feb  9 2017,
  Laser Wave Length:             850nm,
  Admin Status:                  POWER ON,
  Operational Status:            UP

Slot 1 Transceiver 11
  ALU Model Name:                ISFP-10G-C1M   ,
  ALU Model Number:              120383-90   ,
  Hardware Revision:             B   ,
  Serial Number:                 C2110290269     ,
  Manufacture Date:              Oct 29 2021,
  Laser Wave Length:             N/A,
  Admin Status:                  POWER ON,
  Operational Status:            UP

Slot 1 Transceiver 12
  Manufacturer Name:             CTST            ,
  Part Number:                   THCPRJ-0088-0AB ,
  Hardware Revision:             A   ,
  Serial Number:                 F22A07120001    ,
  Manufacture Date:              Jul 12 2022,
  Laser Wave Length:             N/A,
  Admin Status:                  POWER ON,
  Operational Status:            UP



#########################################
show fan
#########################################
ERROR: Command is not supported on this platform


#########################################
show powersupply
#########################################
    Total   0   


#########################################
show temperature
#########################################
Chassis/Device | Current |    Range    | Danger | Thresh |  Status
---------------+---------+-------------+--------+--------+------------
 1/CMMA            52        0 to 75      80       75     UNDER THRESHOLD


#########################################
show system
#########################################
System:
  Description:  Alcatel-Lucent Enterprise OS2260-10 5.2.7.R07 GA, September 23, 2024.,
  Object ID:    *******.4.1.6486.803.*******.1.1.1,
  Up Time:      3 days 23 hours 35 minutes and 32 seconds,
  Contact:      Alcatel-Lucent Enterprise, https://www.al-enterprise.com,
  Name:         OS2260-V11,
  Location:     ALE-GZ-SmartCity-V11,
  Services:     78,
  Date & Time:  TUE JUL 15 2025 19:41:53 (ZP8)
Flash Space:
    Primary CMM:
      Available (bytes):  301502464,
      Comments         :  None


#########################################
show running-directory
#########################################

CONFIGURATION STATUS
  Running CMM              : MASTER-PRIMARY,
  CMM Mode                 : VIRTUAL-CHASSIS MONO CMM,
  Current CMM Slot         : CHASSIS-1 A,
  Running configuration    : WORKING,
  Certify/Restore Status   : CERTIFIED
SYNCHRONIZATION STATUS
  Running Configuration    : NOT SYNCHRONIZED


#########################################
show microcode certified
#########################################
   /flash/certified
   Package           Release                 Size     Description
-----------------+-------------------------+---------+-----------------------------------
Aros.img          5.2.7.R07                 68146292  Alcatel-Lucent OS


#########################################
show microcode working
#########################################
   /flash/working
   Package           Release                 Size     Description
-----------------+-------------------------+---------+-----------------------------------
Aros.img          5.2.7.R07                 68146292  Alcatel-Lucent OS


#########################################
show microcode loaded
#########################################
   /flash/working
   Package           Release                 Size     Description
-----------------+-------------------------+---------+-----------------------------------
Aros.img          5.2.7.R07                 68146292  Alcatel-Lucent OS


#########################################
show license-info
#########################################
                                                   Time (Days)       Upgrade      Expiration  
VC   device   License                  Type        Remaining         Status       Date        
----+------+--------------------+---------------+---------------+--------------+----------------
1       <USER>    <GROUP>                 PERM           NA             NA             NA              
1       0    10G                      PERM           NA             NA             NA              


#########################################
show lldp remote-system
#########################################
Remote LLDP nearest-bridge Agents on Local Port 1/1/3:

    Chassis c8:5a:cf:bf:24:73, Port c8:5a:cf:bf:24:73:
      Remote ID                   = 2,
      Chassis Subtype             = 4 (MAC Address),
      Port Subtype                = 3 (MAC address),
      Port Description            = (null),
      System Name                 = (null),
      System Description          = (null),
      Capabilities Supported      = none supported,
      Capabilities Enabled        = none enabled,
      MED Device Type             = Endpoint Class I,
      MED Capabilities            = Capabilities (01),
      Remote port MAC/PHY AutoNeg = Supported Enabled Capability 0x0001,
      Mau Type                    = Not set

Remote LLDP nearest-bridge Agents on Local Port 1/1/10:

    Chassis 2c:fa:a2:62:9d:21, Port 1027:
      Remote ID                   = 6,
      Chassis Subtype             = 4 (MAC Address),
      Port Subtype                = 7 (Locally assigned),
      Port Description            = (null),
      System Name                 = OS6860E-24,
      System Description          = Alcatel-Lucent Enterprise OS6860E-24 8.10.93.R03 GA, June 14, 2025.,
      Capabilities Supported      = Bridge Router,
      Capabilities Enabled        = Bridge Router,
      Management IP Address       = *******



#########################################
show aaa authentication
#########################################
Service type = Default
  1st authentication server  = local
Service type = Console
  1st authentication server  = local
Service type = Telnet
  Authentication = Use Default,
  1st authentication server  = local
Service type = Ftp
  Authentication = Use Default,
  1st authentication server  = local
Service type = Http
  1st authentication server  = local
Service type = Snmp
  1st authentication server  = local
Service type = Ssh
  Authentication = Use Default,
  1st authentication server  = local


#########################################
show health
#########################################
CMM                    Current   1 Min    1 Hr   1 Day
Resources                         Avg      Avg     Avg
----------------------+---------+-------+-------+-------
CPU                      3        3       5       4
Memory                  79       79      79      79



#########################################
show health all cpu
#########################################
CPU                  Current    1 Min    1 Hr    1 Day
                                Avg      Avg     Avg
-------------------+----------+--------+-------+--------
Slot  1/ 1              3          3        6        4


#########################################
show vlan
#########################################
 vlan    type   admin   oper    ip    mtu          name
------+-------+-------+------+------+------+------------------
1      <USER>       <GROUP>     Ena   Ena    1500    VLAN 1                          
2      std       Ena     Ena   Dis    1500    VLAN 2                          
3      std       Ena     Ena   Dis    1500    VLAN 3                          
4094   vcm       Ena     Dis   Dis    1500    VCM IPC                         


#########################################
show spantree
#########################################
  Spanning Tree Path Cost Mode : AUTO
 Vlan STP Status Protocol Priority
-----+----------+--------+--------------
    1      <USER>       <GROUP>   32768 (0x8000)
    2      ON       RSTP   32768 (0x8000)
    3      ON       RSTP   32768 (0x8000)
 4094     OFF       RSTP   32768 (0x8000)


#########################################
show spantree ports active
#########################################
 Vlan  Port   Oper Status  Path Cost  Role   Note
-----+-------+------------+---------+-------+------
    1   1/1/2     FORW             4   DESG       
    1   1/1/3     FORW             4   DESG       
    1   1/1/7     FORW             4   DESG       
    1   1/1/9     FORW             2   DESG       
    1  1/1/10     FORW             2   ROOT       
    1  1/1/11     FORW             2   DESG       
    1  1/1/12     FORW             2   DESG       
    2   1/1/2     FORW             4   DESG       
    3   1/1/2     FORW             4   DESG       


#########################################
show interfaces status
#########################################
 Chas/                DETECTED-VALUES              CONFIGURED-VALUES    
 Slot/   Admin  Auto  Speed   Duplex  Pause  FEC   Speed   Duplex  Pause  FEC   Link
 Port    Status Nego  (Mbps)                 Det   (Mbps)                 Cfg   Trap  EEE
--------+------+----+--------+------+-------+----+--------+------+-------+-----+-----+---
 1/1/1      en    en     -      -       -    -      1000    Full     -    AUTO  en   dis
 1/1/2      en    en    1000   Full     -    -      1000    Full     -    AUTO  en   dis
 1/1/3      en    en    1000   Full     -    -      1000    Full     -    AUTO  en   dis
 1/1/4      en    en     -      -       -    -      1000    Full     -    AUTO  en   dis
 1/1/5      en    en     -      -       -    -      1000    Full     -    AUTO  en   dis
 1/1/6      en    en     -      -       -    -      1000    Full     -    AUTO  en   dis
 1/1/7      en    en    1000   Full     -    -      1000    Full     -    AUTO  en   dis
 1/1/8      en    en     -      -       -    -      1000    Full     -    AUTO  en   dis
 1/1/9      en   dis   10000   Full     -    -     10000    Full     -    AUTO  en   dis
 1/1/10     en   dis   10000   Full     -    -     10000    Full     -    AUTO  en   dis
 1/1/11     en   dis   10000   Full     -    -     10000    Full     -    AUTO  en   dis
 1/1/12     en   dis   10000   Full     -    -     10000    Full     -    AUTO  en   dis


#########################################
show interfaces counters
#########################################
1/1/2  ,
  InOctets      =           1710803166,  OutOctets      =            680070590,
  InUcastPkts   =              2141975,  OutUcastPkts   =              1906661,
  InMcastPkts   =                 7432,  OutMcastPkts   =               776766,
  InBcastPkts   =                 1869,  OutBcastPkts   =               779750,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                    2,  OutPkts/s      =                    6,
  InBits/s      =                 2656,  OutBits/s      =                 4704
1/1/3  ,
  InOctets      =         226315929313,  OutOctets      =         362591602185,
  InUcastPkts   =            292209728,  OutUcastPkts   =            324796141,
  InMcastPkts   =                 5069,  OutMcastPkts   =               438553,
  InBcastPkts   =                  384,  OutBcastPkts   =               781276,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                 2973,  OutPkts/s      =                 3105,
  InBits/s      =             20306168,  OutBits/s      =             26804272
1/1/7  ,
  InOctets      =             85350275,  OutOctets      =            259499852,
  InUcastPkts   =               447316,  OutUcastPkts   =               664282,
  InMcastPkts   =                 2936,  OutMcastPkts   =               438329,
  InBcastPkts   =                 1252,  OutBcastPkts   =               780374,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                   18,  OutPkts/s      =                   22,
  InBits/s      =                26136,  OutBits/s      =                57744
1/1/9  ,
  InOctets      =                    0,  OutOctets      =            192384191,
  InUcastPkts   =                    0,  OutUcastPkts   =               266611,
  InMcastPkts   =                    0,  OutMcastPkts   =               441247,
  InBcastPkts   =                    0,  OutBcastPkts   =               781630,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                    0,  OutPkts/s      =                    6,
  InBits/s      =                    0,  OutBits/s      =                 4080
1/1/10 ,
  InOctets      =         362723412605,  OutOctets      =         175692576620,
  InUcastPkts   =            313490178,  OutUcastPkts   =            409913411,
  InMcastPkts   =               382573,  OutMcastPkts   =               119307,
  InBcastPkts   =               731612,  OutBcastPkts   =                48467,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                 1011,  OutPkts/s      =                 1175,
  InBits/s      =              7875224,  OutBits/s      =              1390032
1/1/11 ,
  InOctets      =         298672982024,  OutOctets      =         349482270168,
  InUcastPkts   =            414886496,  OutUcastPkts   =            285790133,
  InMcastPkts   =                69413,  OutMcastPkts   =               371868,
  InBcastPkts   =                38994,  OutBcastPkts   =               742653,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                 2211,  OutPkts/s      =                 1941,
  InBits/s      =             19522216,  OutBits/s      =             19430440
1/1/12 ,
  InOctets      =           1479112517,  OutOctets      =           2861881107,
  InUcastPkts   =              3926686,  OutUcastPkts   =              4667350,
  InMcastPkts   =                 2917,  OutMcastPkts   =               438353,
  InBcastPkts   =                 1906,  OutBcastPkts   =               779722,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                    4,  OutPkts/s      =                    8,
  InBits/s      =                 6560,  OutBits/s      =                 7808


#########################################
show ip interface
#########################################
Total 2 interfaces
 Flags (D=Directly-bound)

            Name                 IP Address      Subnet Mask     Status Forward  Device   Flags
--------------------------------+---------------+---------------+------+-------+---------+------
Loopback                         127.0.0.1       ***************     UP      NO Loopback    
management                       ************    *************       UP     YES vlan 1      


#########################################
show ip config
#########################################

IP directed-broadcast   =   OFF,
IP default TTL          =   64
Distributed ARP         =   OFF,



#########################################
show ip protocols
#########################################
IP Protocols
RIP status				= Not Loaded,
OSPF status				= Not Loaded,
ISIS status				= Not Loaded,
BGP status				= Not Loaded,
PIM status				= Not Loaded,
DVMRP status				= Not Loaded,
RIPng status				= Not Loaded,
OSPF3 status				= Not Loaded,
LDP status				= Not Loaded,
VRRP status				= Not Loaded,

#########################################
show ip dos statistics
#########################################

  DoS type                             Attacks detected  
----------------------------------+--------------------------
  port scan                                           0
  ping of death                                       0
  invalid-ip                                          0
  ping overload                                       0
  arp flood                                           0
  arp poison                                          0


#########################################
show snmp statistics
#########################################
From RFC1907
  snmpInPkts                                   = 127902
  snmpOutPkts                                  = 127902
  snmpInBadVersions                            = 0
  snmpInBadCommunityNames                      = 0
  snmpInBadCommunityUses                       = 0
  snmpInASNParseErrs                           = 0
  snmpEnableAuthenTraps                        = disabled(2)
  snmpSilentDrop                               = 0
  snmpProxyDrops                               = 0
  snmpInTooBigs                                = 0
  snmpInNoSuchNames                            = 0
  snmpInBadValues                              = 0
  snmpInReadOnlys                              = 0
  snmpInGenErrs                                = 0
  snmpInTotalReqVars                           = 2543990
  snmpInTotalSetVars                           = 1
  snmpInGetRequests                            = 21790
  snmpInGetNexts                               = 61655
  snmpInSetRequests                            = 1 
  snmpInGetResponses                           = 0
  snmpInTraps                                  = 0
  snmpOutTooBigs                               = 0
  snmpOutNoSuchNames                           = 0
  snmpOutBadValues                             = 0
  snmpOutGenErrs                               = 0
  snmpOutGetRequests                           = 0
  snmpOutGetNexts                              = 0
  snmpOutSetRequests                           = 0
  snmpOutGetResponses                          = 127902
  snmpOutTraps                                 = 0
From RFC2572
  snmpUnknownSecurityModels                    = 0
  snmpInvalidMsgs                              = 0
  snmpUnknownPDUHandlers                       = 0
From RFC2573
  snmpUnavailableContexts                      = 0
  snmpUnknownContexts                          = 0
From RFC2574
  usmStatsUnsupportedSecLevels                 = 0 
  usmStatsNotInTimeWindows                     = 0
  usmStatsUnknownUserNames                     = 0
  usmStatsUnknownEngineIDs                     = 0
  usmStatsWrongDigests                         = 0
  usmStatsDecryptionErrors                     = 0 
From RFC5591
  snmpTsmInvalidCaches                         = 0 
  snmpTsmInadequateSecurityLevels              = 0 
  snmpTsmUnknownPrefixes                       = 0 
  snmpTsmInvalidPrefixes                       = 0 
From RFC5953
  snmpTlstmSessionOpens                        = 0 
  snmpTlstmSessionClientCloses                 = 0 
  snmpTlstmSessionOpenErrors                   = 0 
  snmpTlstmSessionAccepts                      = 0 
  snmpTlstmSessionServerCloses                 = 0 
  snmpTlstmSessionNoSessions                   = 0 
  snmpTlstmSessionInvalidClientCertificates    = 0 
  snmpTlstmSessionUnknownServerCertificate     = 0 
  snmpTlstmSessionInvalidServerCertificates    = 0 
  snmpTlstmSessionInvalidCaches                = 0 
From RFC3411
  snmpEngineID                                 = 80001956039424e18373c1 
  snmpEngineBoots                              = 14 
  snmpEngineTime                               = 344022 
  snmpEngineMaxMessageSize                     = 1500 


#########################################
show virtual-chassis topology
#########################################
Legend: Status suffix "+" means an added unit after last saved topology

Local Chassis: 1
 Oper                                   Config   Oper                          
 Chas  Role         Status              Chas ID  Pri   Group  MAC-Address      
-----+------------+-------------------+--------+-----+------+------------------
 1     <USER>       <GROUP>             1        100   0      94:24:e1:83:73:c1


#########################################
show virtual-chassis consistency
#########################################
Legend: * - denotes mandatory consistency which will affect chassis status
        licenses-info - A: Advanced; B: Data Center;

       Config           Oper                   Oper     Config   
       Chas             Chas    Chas   Hello   Control  Control  
 Chas* ID     Status    Type*   Group* Interv  Vlan*    Vlan     License* 
------+------+---------+-------+------+-------+--------+--------+----------
 1     <USER>      <GROUP>        OS2260  0      15      4094     4094     A         



#########################################
show virtual-chassis vf-link member-port
#########################################



#########################################
show virtual-chassis chassis-reset-list
#########################################
 Chas  Chassis reset list  
-----+---------------------
 1     1,            


#########################################
show virtual-chassis slot-reset-list
#########################################
 Chas  Slot    Reset status
-----+-------+--------------
 1     <USER>       <GROUP>   


#########################################
debug show virtual-chassis status
#########################################

 ID  Level  Parameter                     Value            Timestamp   Status  
----+------+-----------------------------+----------------+-----------+---------
 0   L0     Chassis Identifier            1                19:41:53    OK      
 1   L0     Designated NI Module          1                19:41:53    OK      
 2   L0     Designated NI Module (@L5)    1                20:08:55    OK      
 3   L0     License Configured            Yes              19:41:53    OK      
 4   L0     License Configured (@L5)      Yes              20:08:55    OK      
 5   L0     VFL Links Configured          0                19:41:53    NOK_07  
 6   L0     VFL Links Configured (@L5)    0                20:08:55    NOK_07  
 7   L0     VFL Ports Configured          0                19:41:53    NOK_08  
 8   L0     VFL Ports Configured (@L5)    0                20:08:55    NOK_08  
 11  L0     Chassis Ready Received        Yes              20:08:55    OK      
 12  L1     VFL Intf Oper Status          Down             19:41:53    NOK_09  
 13  L1     VFL Intf Oper Status (@L5)    Down             20:08:55    NOK_09  
 14  L2     VFL LACP Status               Down             19:41:53    NOK_14  
 15  L2     VFL LACP Status (@L5)         Down             20:08:55    NOK_14  
 16  L2     VFL LACP Up -> Down           0                N/A         N/A     
 17  L2     VFL LACP Down -> Up           0                N/A         N/A     
 18  L3     VCM Protocol Role (@L5)       Master           20:08:55    OK      
 19  L3     VCM Protocol Role             Master           19:41:53    OK      
 20  L3     VCM Protocol Status (@L5)     Running          20:08:55    OK      
 21  L3     VCM Protocol Status           Running          19:41:53    OK      
 24  L4     VCM Connection                N/A              19:41:53    N/A     
 25  L4     VCM Connection (@L5)          N/A              20:08:55    N/A     
 26  L5     VCM Synchronization           Single-node      19:41:53    NOK_17  
 27  L6     Chassis Sup Connection        N/A              N/A         N/A     
 28  L6     Remote Flash Mounted          N/A              N/A         N/A     
 29  L6     Image and Config Checked      N/A              N/A         N/A     
 30  L6     VC Takeover Sent              Yes              20:08:55    OK      
 31  L7     VC Takeover Acknowledged      Yes              20:08:57    OK      
 32  L8     System Ready Received         Yes              20:08:57    OK      
 33  L8     RCD Operational Status        N/A              N/A         N/A     
 34  L8     RCD IP Address                N/A              N/A         N/A     

Error/Information Codes Detected:
--------------------------------------------------------------------------------
NOK_07
    There are no virtual-fabric links configured on this switch.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port

NOK_07
    There are no virtual-fabric links configured on this switch.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port

NOK_08
    There are no virtual-fabric member ports configured on this switch.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port configured or assigned to each of the 
    virtual-fabric links.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"

NOK_08
    There are no virtual-fabric member ports configured on this switch.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port configured or assigned to each of the 
    virtual-fabric links.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"

NOK_09
    There are no virtual-fabric member interfaces operationally up.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port interface up on each virtual-fabric link.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"
    -> show interfaces port <chassis>/<slot>/<port> status

NOK_09
    There are no virtual-fabric member interfaces operationally up.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port interface up on each virtual-fabric link.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"
    -> show interfaces port <chassis>/<slot>/<port> status

NOK_14
    The virtual-fabric links configured on this switch are not operationally up.
    If there are multiple links configured, all of them must be operationally up
    in order for this parameter to be reported as OK.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link | grep "<local-chassis-id>/"

NOK_14
    The virtual-fabric links configured on this switch are not operationally up.
    If there are multiple links configured, all of them must be operationally up
    in order for this parameter to be reported as OK.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link | grep "<local-chassis-id>/"

NOK_17
    The virtual-chassis manager protocol did not discover any peer switch
    within the discovery time window (i.e. 4 minutes) starting from the time
    we reached the chassis ready state.
    It is possible that there are no other peers, there are no virtual-fabric
    links configured or operational between the switches, or the virtual-chassis
    manager protocol packets are not going through.
    Troubleshooting Tips:
    a) Check if any peer switches were discovered using:
       -> show virtual-chassis topology
    b) If no peers were discovered, check whether the virtual-fabric links are
       operational
       -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"



#########################################
debug show virtual-chassis connection
#########################################
                          Address           Address                       
 Chas  MAC-Address        Local IP          Remote IP         Status      
-----+------------------+-----------------+-----------------+-------------



#########################################
show cloud-agent status
#########################################
Admin State                     : Disabled,
Activation Server State         : Unknown,
Device State                    : Initial,
Error State                     : None,
Cloud Group                     : -,
DHCP Address                    : -,
DHCP IP Address Mask            : -,
Gateway                         : -,
Activation Server               : activation.myovcloud.com:443,
Network ID                      : -,
NTP Server                      : ************, *************, *************,
DNS Server                      : -,
DNS Domain                      : -,
Proxy Server                    : -,
VPN Server                      : -,
Preprovision Server             : -,
OV Tenant                       : -,
VPN DPD Time (sec)              : 600,
Image Server                    : -,
Image Download Retry Count      : -,
Discovery Interval (min)        : 30,
Time to next Call Home (sec)    : -,
Call Home Timer Status          : Not-Running,
Discovery Retry Count           : 0,
Certificate Status              : -

