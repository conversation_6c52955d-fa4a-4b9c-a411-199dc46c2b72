命令: show configuration snapshot
设备: ************
时间: 2025-07-15 21:45:48.629326
==================================================
! Chassis:
system name "OS2260-V11"
system location "ALE-GZ-SmartCity-V11"

! Configuration:
configuration error-file-limit 2

! Capability Manager:
! Virtual Flow Control:
! Interface:
interfaces port 1/1/1 duplex full
interfaces port 1/1/1 speed 1000
interfaces port 1/1/2 duplex full
interfaces port 1/1/2 speed 1000
interfaces port 1/1/3 duplex full
interfaces port 1/1/3 speed 1000
interfaces port 1/1/4 duplex full
interfaces port 1/1/4 speed 1000
interfaces port 1/1/5 duplex full
interfaces port 1/1/5 speed 1000
interfaces port 1/1/6 duplex full
interfaces port 1/1/6 speed 1000
interfaces port 1/1/7 duplex full
interfaces port 1/1/7 speed 1000
interfaces port 1/1/8 duplex full
interfaces port 1/1/8 speed 1000
interfaces ddm enable
interfaces ddm-trap enable

! Port_Manager: 
! Link Aggregate:
linkagg static agg 1 size 2 admin-state enable 

! VLAN:
vlan 1-3 admin-state enable
vlan 2 members port 1/1/2 tagged
vlan 3 members port 1/1/2 tagged

! Spanning Tree:
spantree vlan 1 admin-state enable 
spantree vlan 2 admin-state enable 
spantree vlan 3 admin-state enable 

! DA-UNP:
! Bridging:
! Port Mirroring:
! Port Mapping:
! IP:
ip service telnet admin-state disable
ip interface "management" address ************ mask ************* vlan 1 ifindex 1
ip service source-ip "management" swlog

! IPv6:
! IPMS:
ip multicast admin-state enable
ip multicast version 2
ipv6 multicast admin-state enable

! AAA:
aaa authentication default "local" 
aaa authentication console "local" 
aaa authentication http "local" 
aaa authentication snmp "local" 


! NTP:
ntp server ************
ntp server *************
ntp server *************
ntp broadcast-client enable
ntp client admin-state enable

! QOS:
qos disable
policy condition "c1" source ip *********** destination tcp-port 22
policy condition "c2" source ip Any destination tcp-port 22
qos apply

! Policy Manager:
! ERP:
! MVRP:
! LLDP:
lldp nearest-bridge chassis tlv management port-description enable system-name enable system-description enable
lldp nearest-bridge chassis tlv management management-address enable
lldp nearest-bridge port 1/1/1-12 notification enable
lldp non-tpmr port 1/1/1-12 notification enable
lldp nearest-customer port 1/1/1-12 notification enable

! UDLD:
! Session Manager:
session prompt default "os2260-#"

! Web:
! Trap Manager:
! Health Monitor:
health threshold memory 5

! System Service:
swlog output socket **********4 10514
system timezone ZP8

! SNMP:
snmp security no-security
snmp community-map mode enable
snmp community-map hash-key fc86c501c784af14 user "snmpv2" enable
snmp community-map hash-key c47fdc198d69417e user "snmpuser" enable

! IP Route Manager:
ip static-route 0.0.0.0/0 gateway ********** metric 1 

! VRRP:
! RIP:
! OSPF:
! RIPng:
! OSPF3:
! LAN Power:
! DHCPv6 Relay:
! DHCPv6 Snooping:
! Virtual Chassis Split Protection:
! DHCP Snooping:
! DHCP Server:
! Loopback Detection:
! OVC:
! IP DHCP RELAY:
! LOOPBACK TEST:
! MGMT AGENT:
