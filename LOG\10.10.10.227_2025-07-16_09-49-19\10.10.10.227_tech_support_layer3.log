
=======
LAYER 3
=======

#########################################
show ip routes
#########################################

 + = Equal cost multipath routes
 Total 3 routes

  Dest Address       Gateway Addr        Age        Protocol 
------------------+-------------------+----------+-----------
  0.0.0.0/0            ************        33d21h   STATIC    
  **********/24        ************        33d21h   LOCAL     
  127.0.0.1/32         127.0.0.1           33d21h   LOCAL     


#########################################
show ip router database
#########################################
Legend: + indicates routes in-use
        b indicates BFD-enabled static route
        i indicates interface static route
        r indicates recursive static route, with following address in brackets

Total IPRM IPv4 routes: 3

  Destination         Gateway                   Interface              Protocol  Metric     Tag      Misc-Info
---------------------+---------------+--------------------------------+--------+-------+----------+-----------------
+  0.0.0.0/0          ************    vlan1                            STATIC         1          0  
+  **********/24      ************    vlan1                            LOCAL          1          0  
+  127.0.0.1/32       127.0.0.1       Loopback                         LOCAL          1          0  


Inactive Static Routes
   Destination       Gateway           Metric        Tag   Misc-Info
--------------------+-----------------+------+----------+-----------------


#########################################
show ip access-list
#########################################
Access Lists: ipv4 configured: 0 ipv6 configured: 0 total configured: 0 max: 0

Address /    Redistribution
Name   Prefix Length      Effect  Control
--------------------+------------------+-------+--------------


#########################################
show ip route-map
#########################################
Route Maps: configured: 0 max: 200


#########################################
show ip redist
#########################################

Source       Destination
Protocol     Protocol     Admin State  Route Map
------------+------------+-----------+--------------------


#########################################
show ip traffic
#########################################

Summary:
Datagrams received
  Total                   =       3373,
  IP header error         =          0,
  Destination IP error    =          0,
  Unknown protocol        =          0,
  Local discards          =          7,
  Delivered to users      =       3195,
  Reassemble needed       =          0,
  Reassembled             =          0,
  Reassemble failed       =          0

Datagrams sent
  Forwarded               =        171,
  Generated               =       3195,
  Local discards          =          0,
  No route discards       =          0,
  Fragmented              =          0,
  Fragment failed         =          0,
  Fragments generated     =          0

Chassis 1 Slot 1:
Datagrams received
  Total                   =       3373,
  IP header error         =          0,
  Destination IP error    =          0,
  Unknown protocol        =          0,
  Local discards          =          7,
  Delivered to users      =       3195,
  Reassemble needed       =          0,
  Reassembled             =          0,
  Reassemble failed       =          0


Datagrams sent
  Forwarded               =        171,
  Generated               =       3195,
  Local discards          =          0,
  No route discards       =          0,
  Fragmented              =          0,
  Fragment failed         =          0,
  Fragments generated     =          0



#########################################
show icmp statistics
#########################################

  Messages                    Received         Sent
---------------------------+----------+-------------
  Total                         144332      1551265
  Error                              0            0
  Destination unreachable       138773      1545673
  Time exceeded                      0            0
  Parameter problem                  0            0
  Source quench                      0            0
  Redirect                           0            0
  Echo request                    5537           55
  Echo reply                        22         5537
  Timestamp Request                  0            0
  Timestamp Reply                    0            0
  Address mask request               0            0
  Address mask reply                 0            0


#########################################
show tcp statistics
#########################################

Total segments received =     83637385,
Error segments received =            0,
Total segments sent     =     84025073,
Segments retransmitted  =      1019157,
Reset segments sent     =      6969692,
Connections initiated   =      7184673,
Connections accepted    =        30542,
Connections established =            1,
Attempt fails           =      6781024,
Established resets      =          649



#########################################
show tcp ports
#########################################

  Local Address     Local Port   Remote Address    Remote Port    State       
------------------+------------+-----------------+--------------+-------------
  0.0.0.0                   22   0.0.0.0                     0    LISTEN        
  0.0.0.0                  111   0.0.0.0                     0    LISTEN        
  0.0.0.0                  443   0.0.0.0                     0    LISTEN        
  0.0.0.0                10161   0.0.0.0                     0    LISTEN        
  0.0.0.0                32053   0.0.0.0                     0    LISTEN        
  ************              22   ************            18342    ESTABLISHED   



#########################################
show udp statistics
#########################################

Total datagrams received   =        75680,
Error datagrams received   =            0,
No port datagrams received =       141817,
Total datagrams sent       =       219152



#########################################
show udp ports
#########################################

  Local Address     Local Port   
------------------+--------------
  0.0.0.0                   67
  0.0.0.0                  111
  0.0.0.0                  123
  0.0.0.0                  161
  0.0.0.0                  863
  0.0.0.0                  955
  0.0.0.0                 1812
  0.0.0.0                 3799
  0.0.0.0                36126
  0.0.0.0                39360
  ************             123
  0.0.0.0                  161
  0.0.0.0                36689



#########################################
show ip vrrp
#########################################
ERROR: specified application not loaded


#########################################
show ip vrrp statistics
#########################################
ERROR: specified application not loaded


#########################################
show arp
#########################################

Total 2 arp entries
 Flags (P=Proxy, A=Authentication, V=VRRP, B=BFD, H=HAVLAN, I=INTF, M=Managed)

 IP Addr           Hardware Addr       Type       Flags   Port              Interface   Name
-----------------+-------------------+----------+-------+-----------------+-----------+---------------------------------
 ************      b0:28:23:12:00:20   DYNAMIC                       1/1/1  vlan1                                       
 ************      30:43:d7:ef:59:79   DYNAMIC                       1/1/1  vlan1                                       



#########################################
show qos log
#########################################
**QOS Log**
10/17/23 12:01:53 Log Init (652e0731).
10/17/23 12:01:53 Connect from API 1/0
10/17/23 12:01:53 Connect socket TCAMCmm
10/17/23 12:01:53 qosmHandleTcamMgrConnect: ret 0 sending Hello.
10/17/23 12:01:53 QoS registered with Chassis Supervisor
10/17/23 12:01:53 QoS registered with MIP library
10/17/23 12:01:53 QoS registered with Vlan Manager
10/17/23 12:01:53 QoS registered with ipCmm
10/17/23 12:01:53 QoS registered with ipv6
10/17/23 12:01:53 qosmHandleTcamMgrMsg: HELLO ACK msg received from TCAM Manager CMM
10/17/23 12:01:53 qoscEpHandleConnectPm: Connected to Port Manager.
10/17/23 12:01:53 Got eoic (652e0731)
10/17/23 12:01:53 Calling cslib_unblock (652e0731)
10/17/23 12:02:15 Connect from API 1/1
10/17/23 12:02:17 add VRF [id 0] name "default"
10/17/23 12:02:46 qosmHandleTcamMgrMsg: SLOT STATUS received from TCAM Manager CMM
10/18/23  8:46:15 VM_PORT_STATE_CHANGE 1:1/9 inactive
10/18/23  8:56:35 VM_PORT_STATE_CHANGE 1:1/9 inactive
10/18/23  9:13:15 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/18/23  9:14:31 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/18/23  9:21:02 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/18/23  9:25:33 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/18/23  9:28:36 VM_PORT_STATE_CHANGE 1:1/7 inactive
10/18/23  9:29:54 VM_PORT_STATE_CHANGE 1:1/7 inactive
10/18/23  9:30:30 VM_PORT_STATE_CHANGE 1:1/7 inactive
10/18/23  9:30:45 VM_PORT_STATE_CHANGE 1:1/6 inactive
10/18/23  9:31:23 VM_PORT_STATE_CHANGE 1:1/6 inactive
10/18/23  9:31:57 VM_PORT_STATE_CHANGE 1:1/6 inactive
10/18/23 10:51:22 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/18/23 12:19:52 VM_PORT_STATE_CHANGE 1:1/4 inactive
10/18/23 13:13:07 VM_PORT_STATE_CHANGE 1:1/4 inactive
10/18/23 13:15:56 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/18/23 13:17:08 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/18/23 13:41:02 VM_PORT_STATE_CHANGE 1:1/9 inactive
10/19/23  6:13:42 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  6:18:16 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  6:19:22 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  6:40:08 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  6:40:14 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  6:40:19 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  6:44:39 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  6:44:53 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  6:44:58 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  6:46:53 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  6:46:58 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  6:47:04 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  7:23:41 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  7:24:14 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:24:19 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:24:26 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:24:48 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:24:53 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:28:55 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:29:04 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:29:09 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:31:19 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:31:24 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:31:30 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:33:38 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:33:44 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:33:48 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:40:06 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/19/23  7:41:29 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  7:43:15 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  7:46:36 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:46:45 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:46:50 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:58:38 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:58:44 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:58:49 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  7:58:55 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  8:01:28 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:01:34 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:01:39 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:03:45 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:03:50 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:03:56 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:05:27 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:05:33 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:05:38 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:08:04 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:08:09 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:08:15 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/19/23  8:10:34 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  8:35:33 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/19/23  8:35:52 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  8:38:01 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  8:48:20 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  8:51:16 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  8:52:07 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  8:52:26 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  8:55:21 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  8:55:23 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  8:55:25 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  8:56:24 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  9:05:56 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  9:06:23 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  9:07:38 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  9:11:10 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/19/23  9:11:31 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  9:11:36 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  9:12:35 VM_PORT_STATE_CHANGE 1:1/4 inactive
10/19/23  9:15:42 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/19/23  9:17:54 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/19/23  9:20:51 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/19/23 10:33:25 VM_PORT_STATE_CHANGE 1:1/8 inactive
10/19/23 11:20:49 VM_PORT_STATE_CHANGE 1:1/9 inactive
10/19/23 11:49:55 VM_PORT_STATE_CHANGE 1:1/9 inactive
10/19/23 12:14:35 VM_PORT_STATE_CHANGE 1:1/9 inactive
10/20/23 10:53:21 VM_PORT_STATE_CHANGE 1:1/9 inactive
10/20/23 11:18:54 VM_PORT_STATE_CHANGE 1:1/8 inactive
10/23/23  6:00:51 VM_PORT_STATE_CHANGE 1:1/9 inactive
10/24/23 11:56:19 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 11:56:25 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 11:56:30 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:14:39 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:14:45 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:14:50 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:33:00 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:33:06 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:33:11 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:35:31 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:35:37 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:35:42 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:53:52 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:53:57 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 12:54:03 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:12:11 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:12:17 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:12:23 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:41:26 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:41:32 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:41:37 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:47:49 VM_PORT_STATE_CHANGE 1:1/8 inactive
10/24/23 13:54:07 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:54:20 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:54:25 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:56:39 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:56:45 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 13:56:49 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 14:13:21 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 14:13:27 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 14:13:32 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 14:15:16 VM_PORT_STATE_CHANGE 1:1/8 inactive
10/24/23 14:16:00 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 14:16:06 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 14:16:12 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/24/23 14:27:58 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/24/23 14:30:29 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/24/23 14:35:16 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/24/23 14:35:44 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/24/23 14:36:43 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/24/23 14:38:07 VM_PORT_STATE_CHANGE 1:1/7 inactive
10/24/23 14:38:13 VM_PORT_STATE_CHANGE 1:1/8 inactive
10/24/23 14:38:27 VM_PORT_STATE_CHANGE 1:1/4 inactive
10/24/23 14:42:40 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/24/23 14:42:48 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/24/23 14:44:42 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/24/23 14:50:34 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/24/23 15:04:07 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/24/23 15:04:29 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/24/23 15:13:37 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/24/23 15:13:49 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/24/23 15:17:00 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/25/23  7:45:23 VM_PORT_STATE_CHANGE 1:1/4 inactive
10/25/23  7:46:43 VM_PORT_STATE_CHANGE 1:1/2 inactive
10/25/23  7:46:50 VM_PORT_STATE_CHANGE 1:1/3 inactive
10/25/23  7:47:41 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23  7:47:47 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23  7:47:53 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23  7:53:23 VM_PORT_STATE_CHANGE 1:1/4 inactive
10/25/23  7:53:35 VM_PORT_STATE_CHANGE 1:1/4 inactive
10/25/23  8:04:52 VM_PORT_STATE_CHANGE 1:1/4 inactive
10/25/23  8:04:58 VM_PORT_STATE_CHANGE 1:1/4 inactive
10/25/23  8:34:41 VM_PORT_STATE_CHANGE 1:1/4 inactive
10/25/23  8:59:45 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/25/23 10:02:48 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 10:02:54 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 10:02:59 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 10:44:45 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 10:44:51 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 10:44:57 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 10:53:01 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 10:53:07 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 10:53:11 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 11:08:55 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/25/23 11:22:20 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 14:33:14 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/25/23 14:36:19 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/25/23 15:21:35 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/25/23 15:24:31 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 15:24:37 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 15:25:19 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 15:25:25 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 15:25:30 VM_PORT_STATE_CHANGE 1:1/5 inactive
10/25/23 15:35:49 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/25/23 15:39:55 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/25/23 15:42:36 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/25/23 15:43:36 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/25/23 15:50:56 VM_PORT_STATE_CHANGE 1:1/1 inactive
10/25/23 15:53:14 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/02/23 11:20:53 VM_PORT_STATE_CHANGE 1:1/6 inactive
11/06/23  6:38:37 VM_PORT_STATE_CHANGE 1:1/2 inactive
11/06/23  6:38:49 VM_PORT_STATE_CHANGE 1:1/3 inactive
11/06/23  6:50:07 VM_PORT_STATE_CHANGE 1:1/3 inactive
11/06/23  6:50:08 VM_PORT_STATE_CHANGE 1:1/2 inactive
11/06/23  6:51:20 VM_PORT_STATE_CHANGE 1:1/3 inactive
11/06/23  6:51:52 VM_PORT_STATE_CHANGE 1:1/2 inactive
11/06/23  6:52:09 VM_PORT_STATE_CHANGE 1:1/2 inactive
11/06/23  6:52:14 VM_PORT_STATE_CHANGE 1:1/2 inactive
11/10/23 22:10:49 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/10/23 22:10:56 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/14/23 11:52:03 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/14/23 11:52:09 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/15/23 14:22:09 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/15/23 14:22:15 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/17/23  4:18:36 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/17/23  4:18:42 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/18/23 22:15:00 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/18/23 22:15:06 VM_PORT_STATE_CHANGE 1:1/1 inactive
11/19/23 13:44:18 VM_PORT_STATE_CHANGE 1:1/5 inactive
11/19/23 13:44:24 VM_PORT_STATE_CHANGE 1:1/5 inactive
11/19/23 13:44:29 VM_PORT_STATE_CHANGE 1:1/5 inactive


#########################################
show qos config
#########################################
QoS Configuration
  Admin                          = enable,
  Trust ports                    = no,
  Log lines                      = 10240,
  Log level                      = 6,
  Log console                    = no,
  Forward log                    = no,
  User-port filter               = spoof ,
  User-port shutdown             = none,
  Phones                         = trusted,
  DEI Mapping                    = disable,
  DEI Marking                    = disable


#########################################
show qos port
#########################################
Slot/                 Default    Default               Bandwidth           DEI
Port    Active  Trust P/DSCP Classification  Physical  Ingress Egress    Map Mark   Type
-------+-------+-----+------+--------------+----------+-------+------+------+------+-------------
1/1/1     Yes      No  0/ 0           DSCP         1G       -       -    No    No   ethernet-1G
1/1/2     Yes      No  0/ 0           DSCP         1G       -       -    No    No   ethernet-1G
1/1/3     Yes      No  0/ 0           DSCP         1G       -       -    No    No   ethernet-1G
1/1/4      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/5     Yes      No  0/ 0           DSCP         1G       -       -    No    No   ethernet-1G
1/1/6      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/7      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/8      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/9      No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/10     No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/11     No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet
1/1/12     No      No  0/ 0           DSCP         0K       -       -    No    No   ethernet


#########################################
show tcam utilization detail
#########################################

Legend:
 C/S/T = Chassis/Slot/TCAM
 PI = Pre-Ingress
 I  = Ingress
 E  = Egress

             App                       App            Resource                             Entry
 C/S/T       Group                     Name             Name                       Stage   Size    Used   Reserved  Available
             Name                                                                                 Entries  Entries   Entries
------+------------------------+--------------------+-----------------------------+-----+--------+-------+---------+-------------------
 1/1/1 SYSHI                    -                     System High                    I      60       60      128       68
 1/1/1 SYSLO                    -                     System Low                     I      30       63      128       65
 1/1/1 CPUQ                     -                     System CPU-Q                   I      30       28      128      100
 1/1/1 -                        AG                    AG-Filter                      I      30        0      256      256
 1/1/1 -                        QOS                   QoS Policy Ingress             I      60        0      256      256
 1/1/1 -                        QOS                   QoS UNP-LIST                   I       0        0        0        0


#########################################
show qos statistics
#########################################
QoS stats
  Spoofed Events       : 0
  NonSpoofed Events    : 0


#########################################
show policy server long
#########################################
No servers


#########################################
show policy server statistics
#########################################
No policy server stats


#########################################
show policy server rules
#########################################
ERROR: display error (incorrect index = 0)


#########################################
show policy server events
#########################################
 Event time                        event description
-----------------+------------------------------------------------------
10/17/23 12:01:53 Initialized policy event log
10/17/23 12:01:53 Initialized LDAP


#########################################
show active policy rule
#########################################
No active rules

#########################################
show active policy list
#########################################
No applied lists
