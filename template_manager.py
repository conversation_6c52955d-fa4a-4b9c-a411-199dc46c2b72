#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import os
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side


class TemplateManager:
    """Excel模板管理类"""
    
    def __init__(self, template_file="template.xlsx"):
        self.template_file = template_file
        
    def create_template(self):
        """创建完整的Excel模板"""
        print("正在创建Excel模板...")
        
        # 创建工作簿
        wb = Workbook()

        # 删除默认工作表
        if wb.active:
            wb.remove(wb.active)
        
        # 创建设备信息工作表
        self._create_device_sheet(wb)
        
        # 创建各厂商命令工作表
        self._create_cisco_sheet(wb)
        self._create_huawei_sheet(wb)
        self._create_ale_sheet(wb)
        self._create_ruijie_sheet(wb)
        self._create_h3c_sheet(wb)
        
        # 保存文件
        wb.save(self.template_file)
        print(f"模板文件已创建: {self.template_file}")
        
        # 打印使用说明
        self._print_usage()
        
    def _create_device_sheet(self, wb):
        """创建设备信息工作表"""
        ws = wb.create_sheet("设备信息", 0)
        
        # 设置表头
        headers = [
            "序号", "状态", "设备IP", "协议", "端口", 
            "用户名", "密码", "特权密码", "设备类型"
        ]
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = self._get_border()
        
        # 添加示例数据
        sample_data = [
            [1, "启用", "***********", "ssh", 22, "admin", "password", "", "cisco_ios"],
            [2, "启用", "***********", "ssh", 22, "admin", "password", "enable_pass", "cisco_ios"],
            [3, "#", "***********", "telnet", 23, "admin", "password", "", "huawei"],
            [4, "启用", "***********", "ssh", 22, "admin", "password", "", "alcatel_aos"],
            [5, "启用", "***********", "ssh", 22, "admin", "password", "", "ruijie_os"],
            [6, "启用", "192.168.1.6", "ssh", 22, "admin", "password", "", "hp_comware"],
        ]
        
        for row, data in enumerate(sample_data, 2):
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.border = self._get_border()
                if str(value) == "#":
                    cell.fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
        
        # 调整列宽
        column_widths = [8, 10, 15, 8, 8, 12, 15, 15, 15]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width
    
    def _create_cisco_sheet(self, wb):
        """创建Cisco设备命令工作表"""
        ws = wb.create_sheet("cisco_ios")
        self._setup_command_sheet(ws, [
            ["启用", "show version"],
            ["启用", "show running-config"],
            ["启用", "show interface status"],
            ["启用", "show ip interface brief"],
            ["启用", "show vlan brief"],
            ["启用", "show spanning-tree"],
            ["启用", "show mac address-table"],
            ["启用", "show inventory"],
            ["启用", "show environment"],
            ["启用", "show cdp neighbors"],
            ["#", "show tech-support"],
        ])
    
    def _create_huawei_sheet(self, wb):
        """创建华为设备命令工作表"""
        ws = wb.create_sheet("huawei")
        self._setup_command_sheet(ws, [
            ["启用", "display version"],
            ["启用", "display current-configuration"],
            ["启用", "display interface brief"],
            ["启用", "display ip interface brief"],
            ["启用", "display vlan"],
            ["启用", "display stp brief"],
            ["启用", "display mac-address"],
            ["启用", "display device"],
            ["启用", "display environment"],
            ["启用", "display lldp neighbor"],
            ["#", "display diagnostic-information"],
        ])
    
    def _create_ale_sheet(self, wb):
        """创建ALE设备命令工作表"""
        ws = wb.create_sheet("alcatel_aos")
        self._setup_command_sheet(ws, [
            ["启用", "show system"],
            ["启用", "show configuration snapshot"],
            ["启用", "show interfaces status"],
            ["启用", "show ip interface"],
            ["启用", "show vlan"],
            ["启用", "show spanning-tree"],
            ["启用", "show mac-address-table"],
            ["启用", "show chassis"],
            ["启用", "show health"],
            ["启用", "show lldp remote-system"],
            ["#", "show tech-support"],
        ])
    
    def _create_ruijie_sheet(self, wb):
        """创建锐捷设备命令工作表"""
        ws = wb.create_sheet("ruijie_os")
        self._setup_command_sheet(ws, [
            ["启用", "show version"],
            ["启用", "show running-config"],
            ["启用", "show interface status"],
            ["启用", "show ip interface brief"],
            ["启用", "show vlan"],
            ["启用", "show spanning-tree"],
            ["启用", "show mac-address-table"],
            ["启用", "show environment"],
            ["启用", "show lldp neighbors"],
            ["#", "show tech-support"],
        ])
    
    def _create_h3c_sheet(self, wb):
        """创建H3C设备命令工作表"""
        ws = wb.create_sheet("hp_comware")
        self._setup_command_sheet(ws, [
            ["启用", "display version"],
            ["启用", "display current-configuration"],
            ["启用", "display interface brief"],
            ["启用", "display ip interface brief"],
            ["启用", "display vlan"],
            ["启用", "display stp brief"],
            ["启用", "display mac-address"],
            ["启用", "display device"],
            ["启用", "display environment"],
            ["启用", "display lldp neighbor-information"],
            ["#", "display diagnostic-information"],
        ])
    
    def _setup_command_sheet(self, ws, commands):
        """设置命令工作表"""
        headers = ["状态", "命令"]
        
        # 设置表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = self._get_border()
        
        # 添加命令数据
        for row, data in enumerate(commands, 2):
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.border = self._get_border()
                if str(value) == "#":
                    cell.fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
        
        # 调整列宽
        ws.column_dimensions['A'].width = 10
        ws.column_dimensions['B'].width = 35
    
    def _get_border(self):
        """获取边框样式"""
        return Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    def _print_usage(self):
        """打印使用说明"""
        print("\n" + "="*60)
        print("Excel模板使用说明:")
        print("="*60)
        print("1. 设备信息工作表:")
        print("   - 序号: 设备编号")
        print("   - 状态: 填写'#'跳过该设备，其他值表示启用")
        print("   - 设备IP: 设备管理IP地址")
        print("   - 协议: ssh 或 telnet")
        print("   - 端口: SSH端口(22) 或 Telnet端口(23)")
        print("   - 用户名: 登录用户名")
        print("   - 密码: 登录密码")
        print("   - 特权密码: enable密码(可选)")
        print("   - 设备类型: netmiko支持的设备类型")
        print()
        print("2. 支持的设备类型:")
        print("   - cisco_ios: Cisco IOS设备")
        print("   - huawei: 华为设备")
        print("   - alcatel_aos: ALE设备")
        print("   - ruijie_os: 锐捷设备")
        print("   - hp_comware: H3C设备")
        print()
        print("3. 命令配置工作表:")
        print("   - 每种设备类型对应一个工作表")
        print("   - 状态列填写'#'跳过该命令")
        print("   - 命令列填写要执行的CLI命令")
        print()
        print("4. 使用方法:")
        print("   - 配置完成后运行: python network_inspection.py")
        print("   - 或使用启动脚本: python start.py")
        print("="*60)
    
    def validate_template(self):
        """验证模板文件"""
        if not os.path.exists(self.template_file):
            print(f"模板文件不存在: {self.template_file}")
            return False
        
        try:
            wb = load_workbook(self.template_file)
            
            # 检查必需的工作表
            required_sheets = ["设备信息"]
            for sheet in required_sheets:
                if sheet not in wb.sheetnames:
                    print(f"缺少必需的工作表: {sheet}")
                    return False
            
            # 检查设备信息工作表结构
            ws = wb["设备信息"]
            if ws.max_row < 2:
                print("设备信息工作表没有数据行")
                return False
            
            print("模板文件验证通过")
            return True
            
        except Exception as e:
            print(f"模板文件验证失败: {e}")
            return False


def main():
    """主函数"""
    template_manager = TemplateManager()
    
    print("Excel模板管理工具")
    print("1. 创建新模板")
    print("2. 验证现有模板")
    print("0. 退出")
    
    choice = input("请选择操作: ").strip()
    
    if choice == "1":
        template_manager.create_template()
    elif choice == "2":
        template_manager.validate_template()
    elif choice == "0":
        print("退出")
    else:
        print("无效选择")


if __name__ == '__main__':
    main()
