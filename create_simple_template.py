#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
简化版模板创建脚本
创建基本的Excel模板用于网络设备巡检
"""

try:
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("警告: openpyxl未安装，请运行: pip install openpyxl")


def create_basic_template():
    """创建基本的Excel模板"""
    if not OPENPYXL_AVAILABLE:
        print("无法创建Excel模板，请先安装openpyxl")
        return False
    
    try:
        # 创建工作簿
        wb = Workbook()
        
        # 获取默认工作表并重命名
        ws = wb.active
        ws.title = "设备信息"
        
        # 设置表头
        headers = [
            "序号", "状态", "设备IP", "协议", "端口", 
            "用户名", "密码", "特权密码", "设备类型"
        ]
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
        
        # 添加示例数据
        sample_data = [
            [1, "启用", "***********", "ssh", 22, "admin", "password", "", "cisco_ios"],
            [2, "启用", "***********", "ssh", 22, "admin", "password", "enable_pass", "cisco_ios"],
            [3, "#", "***********", "telnet", 23, "admin", "password", "", "huawei"],
            [4, "启用", "***********", "ssh", 22, "admin", "password", "", "alcatel_aos"],
        ]
        
        for row, data in enumerate(sample_data, 2):
            for col, value in enumerate(data, 1):
                ws.cell(row=row, column=col, value=value)
        
        # 调整列宽
        column_widths = [8, 10, 15, 8, 8, 12, 15, 15, 15]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width
        
        # 创建Cisco命令工作表
        ws_cisco = wb.create_sheet("cisco_ios")
        cisco_headers = ["状态", "命令"]
        
        for col, header in enumerate(cisco_headers, 1):
            cell = ws_cisco.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
        
        cisco_commands = [
            ["启用", "show version"],
            ["启用", "show running-config"],
            ["启用", "show interface status"],
            ["启用", "show ip interface brief"],
            ["启用", "show vlan brief"],
            ["启用", "show spanning-tree"],
            ["启用", "show mac address-table"],
            ["#", "show tech-support"],
        ]
        
        for row, data in enumerate(cisco_commands, 2):
            for col, value in enumerate(data, 1):
                ws_cisco.cell(row=row, column=col, value=value)
        
        ws_cisco.column_dimensions['A'].width = 10
        ws_cisco.column_dimensions['B'].width = 30
        
        # 创建华为命令工作表
        ws_huawei = wb.create_sheet("huawei")
        
        for col, header in enumerate(cisco_headers, 1):
            cell = ws_huawei.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
        
        huawei_commands = [
            ["启用", "display version"],
            ["启用", "display current-configuration"],
            ["启用", "display interface brief"],
            ["启用", "display ip interface brief"],
            ["启用", "display vlan"],
            ["启用", "display stp brief"],
            ["启用", "display mac-address"],
            ["#", "display diagnostic-information"],
        ]
        
        for row, data in enumerate(huawei_commands, 2):
            for col, value in enumerate(data, 1):
                ws_huawei.cell(row=row, column=col, value=value)
        
        ws_huawei.column_dimensions['A'].width = 10
        ws_huawei.column_dimensions['B'].width = 35
        
        # 创建ALE命令工作表
        ws_ale = wb.create_sheet("alcatel_aos")
        
        for col, header in enumerate(cisco_headers, 1):
            cell = ws_ale.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
        
        ale_commands = [
            ["启用", "show system"],
            ["启用", "show configuration snapshot"],
            ["启用", "show interfaces status"],
            ["启用", "show ip interface"],
            ["启用", "show vlan"],
            ["启用", "show spanning-tree"],
            ["启用", "show mac-address-table"],
            ["启用", "show chassis"],
            ["启用", "show health"],
            ["#", "show tech-support"],
        ]
        
        for row, data in enumerate(ale_commands, 2):
            for col, value in enumerate(data, 1):
                ws_ale.cell(row=row, column=col, value=value)
        
        ws_ale.column_dimensions['A'].width = 10
        ws_ale.column_dimensions['B'].width = 35
        
        # 保存文件
        filename = "template.xlsx"
        wb.save(filename)
        print(f"✓ 模板文件已创建: {filename}")
        
        print("\n使用说明:")
        print("1. 在'设备信息'工作表中配置要巡检的设备")
        print("2. 状态列填写'#'表示跳过该设备")
        print("3. 设备类型支持: cisco_ios, huawei, alcatel_aos 等")
        print("4. 在对应的设备类型工作表中配置巡检命令")
        print("5. 命令状态列填写'#'表示跳过该命令")
        
        return True
        
    except Exception as e:
        print(f"创建模板失败: {e}")
        return False


def create_csv_template():
    """创建CSV格式的模板作为备选方案"""
    try:
        # 创建设备信息CSV
        device_csv = """序号,状态,设备IP,协议,端口,用户名,密码,特权密码,设备类型
1,启用,***********,ssh,22,admin,password,,cisco_ios
2,启用,***********,ssh,22,admin,password,enable_pass,cisco_ios
3,#,***********,telnet,23,admin,password,,huawei
4,启用,***********,ssh,22,admin,password,,alcatel_aos"""
        
        with open("设备信息.csv", "w", encoding="utf-8") as f:
            f.write(device_csv)
        
        # 创建命令CSV
        cisco_csv = """状态,命令
启用,show version
启用,show running-config
启用,show interface status
启用,show ip interface brief
启用,show vlan brief
启用,show spanning-tree
启用,show mac address-table
#,show tech-support"""
        
        with open("cisco_ios_commands.csv", "w", encoding="utf-8") as f:
            f.write(cisco_csv)
        
        print("✓ CSV模板文件已创建:")
        print("  - 设备信息.csv")
        print("  - cisco_ios_commands.csv")
        print("\n注意: 建议安装openpyxl使用Excel格式")
        
        return True
        
    except Exception as e:
        print(f"创建CSV模板失败: {e}")
        return False


def main():
    """主函数"""
    print("网络设备巡检模板创建工具")
    print("=" * 40)
    
    # 尝试创建Excel模板
    if create_basic_template():
        print("\n✓ Excel模板创建成功")
    else:
        print("\n! Excel模板创建失败，尝试创建CSV模板")
        if create_csv_template():
            print("✓ CSV模板创建成功")
        else:
            print("✗ 模板创建失败")
            return
    
    print("\n下一步:")
    print("1. 编辑模板文件，配置您的设备信息")
    print("2. 运行巡检程序: python network_inspection.py")


if __name__ == '__main__':
    main()
