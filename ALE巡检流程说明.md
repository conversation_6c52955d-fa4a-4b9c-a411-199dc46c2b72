# ALE设备巡检流程说明

## 巡检流程概述

根据您的要求，巡检流程已经调整为：

1. **直接使用现有Excel文件** - 不需要创建模板步骤
2. **ALE设备特殊处理** - 执行`show tech-support`命令
3. **TFTP文件传输** - 下载根目录的三个日志文件
4. **文件命名规范** - 保存的日志文件名包含设备IP
5. **完成后处理** - 压缩LOG文件夹并发送邮件

## 文件结构

```
项目目录/
├── ale_inspection.py          # ALE专用巡检程序
├── simple_inspection.py       # 通用巡检程序（已增强ALE支持）
├── tftp_downloader.py         # TFTP文件下载工具
├── connect.py                 # 原有连接模块
├── send_email.py              # 原有邮件模块
├── zip_file.py                # 原有压缩模块
├── template.xlsx              # 现有Excel配置文件
└── LOG/                       # 巡检结果目录
    ├── ***********_20241215_143022/
    │   ├── ***********_tech_support_layer3.log
    │   ├── ***********_tech_support_layer2.log
    │   ├── ***********_tech_support.log
    │   └── 其他命令输出文件...
    └── inspection_results_20241215_143022.zip
```

## ALE设备特殊处理流程

### 1. 检测ALE设备
程序会自动检测设备类型中包含`alcatel`或`ale`的设备。

### 2. 执行tech-support命令
```bash
show tech-support
```

### 3. 下载日志文件
程序会尝试通过TFTP下载以下文件：
- `tech_support_layer3.log`
- `tech_support_layer2.log`
- `tech_support.log`

### 4. 文件命名规范
下载的文件会重命名为：
- `{设备IP}_tech_support_layer3.log`
- `{设备IP}_tech_support_layer2.log`
- `{设备IP}_tech_support.log`

## 使用方法

### 方法一：使用ALE专用程序（推荐）
```bash
python ale_inspection.py
```

### 方法二：使用通用程序
```bash
# 完整巡检流程
python simple_inspection.py --auto

# 仅设备信息采集
python simple_inspection.py --collect

# 交互式模式
python simple_inspection.py
```

## Excel配置要求

### 设备信息工作表
确保Excel文件包含以下列：

| 序号 | 状态 | 设备IP | 协议 | 端口 | 用户名 | 密码 | 特权密码 | 设备类型 |
|------|------|--------|------|------|--------|------|----------|----------|
| 1 | 启用 | *********** | ssh | 22 | admin | password | | alcatel_aos |
| 2 | 启用 | *********** | ssh | 22 | admin | password | | cisco_ios |

**重要说明：**
- ALE设备的设备类型请使用：`alcatel_aos`
- 状态列填写`#`可跳过该设备

### ALE设备命令工作表
在`alcatel_aos`工作表中配置命令：

| 状态 | 命令 |
|------|------|
| 启用 | show system |
| 启用 | show configuration snapshot |
| 启用 | show interfaces status |
| 启用 | show vlan |
| 启用 | show health |

## TFTP配置说明

### TFTP服务要求
1. **ALE设备端**：
   - 确保TFTP服务已启用
   - 确保生成的日志文件在根目录
   - 确保文件权限允许读取

2. **客户端端**：
   - 确保网络连通性
   - 确保防火墙允许TFTP（UDP 69端口）

### TFTP测试
```bash
# 测试TFTP连接
python tftp_downloader.py
```

## 故障排除

### 1. TFTP下载失败
**现象**：程序显示"TFTP下载失败"

**可能原因**：
- ALE设备TFTP服务未启用
- 网络连接问题
- 防火墙阻止TFTP
- 日志文件未生成

**解决方案**：
1. 检查ALE设备TFTP配置
2. 测试网络连通性
3. 检查防火墙设置
4. 手动验证日志文件是否存在

### 2. 日志文件不存在
**现象**：TFTP连接正常但文件下载失败

**可能原因**：
- tech-support命令执行失败
- 文件生成时间较长
- 文件路径不正确

**解决方案**：
1. 增加等待时间
2. 手动执行tech-support命令验证
3. 检查设备存储空间

### 3. 备用处理机制
如果TFTP下载失败，程序会：
1. 创建备用记录文件
2. 保存tech-support命令输出
3. 提供手动下载指导

## 输出结果

### 成功情况
```
LOG/
├── ***********_20241215_143022/
│   ├── ***********_tech_support_layer3.log    # TFTP下载的文件
│   ├── ***********_tech_support_layer2.log    # TFTP下载的文件
│   ├── ***********_tech_support.log           # TFTP下载的文件
│   ├── ***********_show_system.txt            # 常规命令输出
│   └── ***********_show_health.txt            # 常规命令输出
└── inspection_results_20241215_143022.zip     # 压缩包
```

### 失败情况
```
LOG/
├── ***********_20241215_143022/
│   ├── ***********_tech_support_layer3.log.failed  # 失败记录
│   ├── ***********_tech_support_layer2.log         # 备用记录
│   └── ***********_tech_support.log                # 备用记录
└── inspection_results_20241215_143022.zip          # 压缩包
```

## 邮件通知

巡检完成后，程序会：
1. 自动压缩LOG文件夹
2. 调用邮件发送功能
3. 发送压缩包作为附件

**注意**：需要在`send_email.py`中配置邮件参数。

## 最佳实践

1. **巡检前准备**：
   - 确认Excel配置正确
   - 测试设备连通性
   - 验证TFTP服务状态

2. **巡检过程**：
   - 监控程序输出
   - 注意错误信息
   - 检查LOG目录生成

3. **巡检后处理**：
   - 验证压缩包内容
   - 确认邮件发送成功
   - 清理旧的日志文件

## 命令行选项

```bash
# 完整巡检（推荐）
python ale_inspection.py

# 通用巡检（支持ALE）
python simple_inspection.py --auto

# 仅连接测试
python simple_inspection.py --test

# 交互式选择
python simple_inspection.py
```

---

**注意**：此流程专门针对ALE设备的tech-support日志下载需求进行了优化，同时保持对其他厂商设备的兼容性。
