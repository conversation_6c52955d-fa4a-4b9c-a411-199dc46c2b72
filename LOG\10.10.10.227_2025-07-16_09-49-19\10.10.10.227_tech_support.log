
=====
BASIC
=====

#########################################
show hardware-info
#########################################
Chassis 1
CPU Manufacturer                  : Realtek
CPU Model                         : MIPS
Flash Manufacturer                : Kioxia
Flash size                        : 465526784 bytes
RAM Manufacturer                  : Winbond 
RAM size                          : 511188kB
FPGA Manufacturer                 : Lattice
FPGA version                      : 0.5
U-Boot Version                    : 5.1.8.R01
Power Supplies Present            : 1
NIs Present                       : 1,-

POE:  RTL8238/GD32E230G8U6  *******




#########################################
show chassis
#########################################
Local Chassis ID 1 (Master)
  Model Name:                    OS2260-P10,
  Module Type:                   0x800e2100,
  Description:                   Chassis,
  Part Number:                   904210-90,
  Hardware Revision:             01,
  Serial Number:                 WKC213100465,
  Manufacture Date:              Aug  3 2021,
  Admin Status:                  POWER ON,
  Operational Status:            UP,
  Number Of Resets:              24,
  MAC Address:                   94:24:e1:85:de:8d



#########################################
show module long
#########################################
Module in slot CMM-A
  Model Name:                    OS2260-P10,
  Module Type:                   0x800e2100,
  Description:                   8G POE 4SFP,
  Part Number:                   904210-90,
  Hardware Revision:             01,
  Serial Number:                 WKC213100465,
  Manufacture Date:              Aug  3 2021,
  FPGA - Physical 1:             0.5,
  Admin Status:                  POWER ON,
  Operational Status:            UP,
  Max Power:                     14,
  CPU Model Type:                RTL 9300,
  MAC Address:                   94:24:e1:85:de:8d,

Module in chassis 1 slot 1
  Model Name:                    OS2260-P10,
  Module Type:                   0x800e2100,
  Description:                   8G POE 4SFP,
  Part Number:                   904210-90,
  Hardware Revision:             01,
  Serial Number:                 WKC213100465,
  Manufacture Date:              Aug  3 2021,
  FPGA - Physical 1:             0.5,
  Admin Status:                  POWER ON,
  Operational Status:            UP,
  Max Power:                     14,
  CPU Model Type:                RTL 9300,
  MAC Address:                   94:24:e1:85:de:94,
  UBOOT Version:                 5.1.8.R01
  POE-Software Version:          RTL8238/GD32E230G8U6  *******



#########################################
show transceivers
#########################################

Chassis 1 No Transceivers present Chassis ID 1 


#########################################
show fan
#########################################
ERROR: Command is not supported on this platform


#########################################
show powersupply
#########################################
             Total     PS
Chassis/PS   Power     Type     Status   Location
-----------+---------+--------+--------+-----------
 1/1         120       AC       UP       Internal
    Total   120 


#########################################
show temperature
#########################################
Chassis/Device | Current |    Range    | Danger | Thresh |  Status
---------------+---------+-------------+--------+--------+------------
 1/CMMA            44        0 to 75      80       75     UNDER THRESHOLD


#########################################
show system
#########################################
System:
  Description:  Alcatel-Lucent Enterprise OS2260-P10 5.2.7.R07 GA, September 23, 2024.,
  Object ID:    *******.4.1.6486.803.*******.1.1.2,
  Up Time:      4 days 11 hours 55 minutes and 15 seconds,
  Contact:      Alcatel-Lucent Enterprise, https://www.al-enterprise.com,
  Name:         OS2260,
  Location:     ALE-GZ-SmartCity-V9,
  Services:     78,
  Date & Time:  WED JUL 16 2025 09:49:18 (ZP8)
Flash Space:
    Primary CMM:
      Available (bytes):  223297536,
      Comments         :  None


#########################################
show running-directory
#########################################

CONFIGURATION STATUS
  Running CMM              : MASTER-PRIMARY,
  CMM Mode                 : VIRTUAL-CHASSIS MONO CMM,
  Current CMM Slot         : CHASSIS-1 A,
  Running configuration    : WORKING,
  Certify/Restore Status   : CERTIFIED
SYNCHRONIZATION STATUS
  Running Configuration    : NOT SYNCHRONIZED


#########################################
show microcode certified
#########################################
   /flash/certified
   Package           Release                 Size     Description
-----------------+-------------------------+---------+-----------------------------------
Aros.img          5.2.7.R07                 68146292  Alcatel-Lucent OS


#########################################
show microcode working
#########################################
   /flash/working
   Package           Release                 Size     Description
-----------------+-------------------------+---------+-----------------------------------
Aros.img          5.2.7.R07                 68146292  Alcatel-Lucent OS


#########################################
show microcode loaded
#########################################
   /flash/working
   Package           Release                 Size     Description
-----------------+-------------------------+---------+-----------------------------------
Aros.img          5.2.7.R07                 68146292  Alcatel-Lucent OS


#########################################
show license-info
#########################################
                                                   Time (Days)       Upgrade      Expiration  
VC   device   License                  Type        Remaining         Status       Date        
----+------+--------------------+---------------+---------------+--------------+----------------
1       <USER>    <GROUP>                 PERM           NA             NA             NA              
1       0    10G                      PERM           NA             NA             NA              


#########################################
show lldp remote-system
#########################################
Remote LLDP nearest-bridge Agents on Local Port 1/1/3:

    Chassis dc:08:56:0c:d4:f0, Port dc:08:56:0c:d4:f0:
      Remote ID                   = 1,
      Chassis Subtype             = 4 (MAC Address),
      Port Subtype                = 3 (MAC address),
      Port Description            = Alcatel-Lucent Enterprise OAW-AP1221 eth0,
      System Name                 = AP-D4:F0,
      System Description          = Alcatel-Lucent Enterprise OAW-AP1221 ********,
      Capabilities Supported      = Bridge WLAN AP Router Station Only,
      Capabilities Enabled        = Bridge WLAN AP Router,
      Management IP Address       = ************,
      MED Device Type             = Network Connectivity,
      MED Capabilities            = Capabilities | Power via MDI-PD(33),
      MED Extension TLVs Present  = Network Policy| Inventory,
      Remote port MAC/PHY AutoNeg = Supported Enabled Capability 0x6cc1,
      Mau Type                    = 1000BaseTFD - Four-pair Category 5 UTP full duplex mode



#########################################
show aaa authentication
#########################################
Service type = Default
  1st authentication server  = local
Service type = Console
  1st authentication server  = local
Service type = Telnet
  Authentication = Use Default,
  1st authentication server  = local
Service type = Ftp
  Authentication = Use Default,
  1st authentication server  = local
Service type = Http
  1st authentication server  = local
Service type = Snmp
  1st authentication server  = local
Service type = Ssh
  Authentication = Use Default,
  1st authentication server  = local


#########################################
show health
#########################################
CMM                    Current   1 Min    1 Hr   1 Day
Resources                         Avg      Avg     Avg
----------------------+---------+-------+-------+-------
CPU                      4        2       4       3
Memory                  79       79      79      79



#########################################
show health all cpu
#########################################
CPU                  Current    1 Min    1 Hr    1 Day
                                Avg      Avg     Avg
-------------------+----------+--------+-------+--------
Slot  1/ 1             27          6        4        3


#########################################
show vlan
#########################################
 vlan    type   admin   oper    ip    mtu          name
------+-------+-------+------+------+------+------------------
1      <USER>       <GROUP>     Ena   Ena    1500    VLAN 1                          
30     std       Ena     Dis   Ena    1500    VLAN 30                         
4094   vcm       Ena     Dis   Dis    1500    VCM IPC                         


#########################################
show spantree
#########################################
  Spanning Tree Path Cost Mode : AUTO
 Vlan STP Status Protocol Priority
-----+----------+--------+--------------
    1      <USER>       <GROUP>   32768 (0x8000)
   30      ON       RSTP   32768 (0x8000)
 4094     OFF       RSTP   32768 (0x8000)


#########################################
show spantree ports active
#########################################
 Vlan  Port   Oper Status  Path Cost  Role   Note
-----+-------+------------+---------+-------+------
    1   1/1/3     FORW             4   ROOT       


#########################################
show interfaces status
#########################################
 Chas/                DETECTED-VALUES              CONFIGURED-VALUES    
 Slot/   Admin  Auto  Speed   Duplex  Pause  FEC   Speed   Duplex  Pause  FEC   Link
 Port    Status Nego  (Mbps)                 Det   (Mbps)                 Cfg   Trap  EEE
--------+------+----+--------+------+-------+----+--------+------+-------+-----+-----+---
 1/1/1      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   dis
 1/1/2      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   dis
 1/1/3      en    en    1000   Full     -    -      Auto    Auto     -    AUTO  en   dis
 1/1/4      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   dis
 1/1/5      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   dis
 1/1/6      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   dis
 1/1/7      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   dis
 1/1/8      en    en     -      -       -    -      Auto    Auto     -    AUTO  en   dis
 1/1/9      en   dis     -      -       -    -     10000    Full     -    AUTO  en   dis
 1/1/10     en   dis     -      -       -    -     10000    Full     -    AUTO  en   dis
 1/1/11     en   dis     -      -       -    -     10000    Full     -    AUTO  en   dis
 1/1/12     en   dis     -      -       -    -     10000    Full     -    AUTO  en   dis


#########################################
show interfaces counters
#########################################
1/1/3  ,
  InOctets      =            201923482,  OutOctets      =             88903170,
  InUcastPkts   =               147510,  OutUcastPkts   =               145649,
  InMcastPkts   =               468122,  OutMcastPkts   =                38702,
  InBcastPkts   =               812076,  OutBcastPkts   =                 4767,
  InPauseFrames =                    0,  OutPauseFrames =                    0,
  InPkts/s      =                   12,  OutPkts/s      =                   18,
  InBits/s      =                 9376,  OutBits/s      =                19280


#########################################
show ip interface
#########################################
Total 5 interfaces
 Flags (D=Directly-bound)

            Name                 IP Address      Subnet Mask     Status Forward  Device   Flags
--------------------------------+---------------+---------------+------+-------+---------+------
Loopback                         127.0.0.1       ***************     UP      NO Loopback    
vlan 10 for office               ************    *************     DOWN      NO vlan 10     
vlan 20 for guest                ************    *************     DOWN      NO vlan 20     
vlan1                            ************    *************       UP     YES vlan 1      
vlan30                           ***********     *************     DOWN      NO vlan 30     


#########################################
show ip config
#########################################

IP directed-broadcast   =   OFF,
IP default TTL          =   64
Distributed ARP         =   OFF,



#########################################
show ip protocols
#########################################
IP Protocols
RIP status				= Not Loaded,
OSPF status				= Not Loaded,
ISIS status				= Not Loaded,
BGP status				= Not Loaded,
PIM status				= Not Loaded,
DVMRP status				= Not Loaded,
RIPng status				= Not Loaded,
OSPF3 status				= Not Loaded,
LDP status				= Not Loaded,
VRRP status				= Not Loaded,

#########################################
show ip dos statistics
#########################################

  DoS type                             Attacks detected  
----------------------------------+--------------------------
  port scan                                           0
  ping of death                                       0
  invalid-ip                                          0
  ping overload                                       0
  arp flood                                           0
  arp poison                                          0


#########################################
show snmp statistics
#########################################
From RFC1907
  snmpInPkts                                   = 138823
  snmpOutPkts                                  = 138823
  snmpInBadVersions                            = 0
  snmpInBadCommunityNames                      = 0
  snmpInBadCommunityUses                       = 0
  snmpInASNParseErrs                           = 0
  snmpEnableAuthenTraps                        = disabled(2)
  snmpSilentDrop                               = 0
  snmpProxyDrops                               = 0
  snmpInTooBigs                                = 0
  snmpInNoSuchNames                            = 0
  snmpInBadValues                              = 0
  snmpInReadOnlys                              = 0
  snmpInGenErrs                                = 0
  snmpInTotalReqVars                           = 2847422
  snmpInTotalSetVars                           = 1
  snmpInGetRequests                            = 23781
  snmpInGetNexts                               = 64817
  snmpInSetRequests                            = 1 
  snmpInGetResponses                           = 0
  snmpInTraps                                  = 0
  snmpOutTooBigs                               = 0
  snmpOutNoSuchNames                           = 0
  snmpOutBadValues                             = 0
  snmpOutGenErrs                               = 0
  snmpOutGetRequests                           = 0
  snmpOutGetNexts                              = 0
  snmpOutSetRequests                           = 0
  snmpOutGetResponses                          = 138823
  snmpOutTraps                                 = 0
From RFC2572
  snmpUnknownSecurityModels                    = 0
  snmpInvalidMsgs                              = 0
  snmpUnknownPDUHandlers                       = 0
From RFC2573
  snmpUnavailableContexts                      = 0
  snmpUnknownContexts                          = 0
From RFC2574
  usmStatsUnsupportedSecLevels                 = 0 
  usmStatsNotInTimeWindows                     = 0
  usmStatsUnknownUserNames                     = 0
  usmStatsUnknownEngineIDs                     = 0
  usmStatsWrongDigests                         = 0
  usmStatsDecryptionErrors                     = 0 
From RFC5591
  snmpTsmInvalidCaches                         = 0 
  snmpTsmInadequateSecurityLevels              = 0 
  snmpTsmUnknownPrefixes                       = 0 
  snmpTsmInvalidPrefixes                       = 0 
From RFC5953
  snmpTlstmSessionOpens                        = 0 
  snmpTlstmSessionClientCloses                 = 0 
  snmpTlstmSessionOpenErrors                   = 0 
  snmpTlstmSessionAccepts                      = 0 
  snmpTlstmSessionServerCloses                 = 0 
  snmpTlstmSessionNoSessions                   = 0 
  snmpTlstmSessionInvalidClientCertificates    = 0 
  snmpTlstmSessionUnknownServerCertificate     = 0 
  snmpTlstmSessionInvalidServerCertificates    = 0 
  snmpTlstmSessionInvalidCaches                = 0 
From RFC3411
  snmpEngineID                                 = 80001956039424e185de8d 
  snmpEngineBoots                              = 24 
  snmpEngineTime                               = 388408 
  snmpEngineMaxMessageSize                     = 1500 


#########################################
show virtual-chassis topology
#########################################
Legend: Status suffix "+" means an added unit after last saved topology

Local Chassis: 1
 Oper                                   Config   Oper                          
 Chas  Role         Status              Chas ID  Pri   Group  MAC-Address      
-----+------------+-------------------+--------+-----+------+------------------
 1     <USER>       <GROUP>             1        100   0      94:24:e1:85:de:8d


#########################################
show virtual-chassis consistency
#########################################
Legend: * - denotes mandatory consistency which will affect chassis status
        licenses-info - A: Advanced; B: Data Center;

       Config           Oper                   Oper     Config   
       Chas             Chas    Chas   Hello   Control  Control  
 Chas* ID     Status    Type*   Group* Interv  Vlan*    Vlan     License* 
------+------+---------+-------+------+-------+--------+--------+----------
 1     <USER>      <GROUP>        OS2260  0      15      4094     4094     A         



#########################################
show virtual-chassis vf-link member-port
#########################################



#########################################
show virtual-chassis chassis-reset-list
#########################################
 Chas  Chassis reset list  
-----+---------------------
 1     1,            


#########################################
show virtual-chassis slot-reset-list
#########################################
 Chas  Slot    Reset status
-----+-------+--------------
 1     <USER>       <GROUP>   


#########################################
debug show virtual-chassis status
#########################################

 ID  Level  Parameter                     Value            Timestamp   Status  
----+------+-----------------------------+----------------+-----------+---------
 0   L0     Chassis Identifier            1                09:49:18    OK      
 1   L0     Designated NI Module          1                09:49:18    OK      
 2   L0     Designated NI Module (@L5)    1                21:55:57    OK      
 3   L0     License Configured            Yes              09:49:18    OK      
 4   L0     License Configured (@L5)      Yes              21:55:57    OK      
 5   L0     VFL Links Configured          0                09:49:18    NOK_07  
 6   L0     VFL Links Configured (@L5)    0                21:55:57    NOK_07  
 7   L0     VFL Ports Configured          0                09:49:18    NOK_08  
 8   L0     VFL Ports Configured (@L5)    0                21:55:57    NOK_08  
 11  L0     Chassis Ready Received        Yes              21:55:57    OK      
 12  L1     VFL Intf Oper Status          Down             09:49:18    NOK_09  
 13  L1     VFL Intf Oper Status (@L5)    Down             21:55:57    NOK_09  
 14  L2     VFL LACP Status               Down             09:49:18    NOK_14  
 15  L2     VFL LACP Status (@L5)         Down             21:55:57    NOK_14  
 16  L2     VFL LACP Up -> Down           0                N/A         N/A     
 17  L2     VFL LACP Down -> Up           0                N/A         N/A     
 18  L3     VCM Protocol Role (@L5)       Master           21:55:57    OK      
 19  L3     VCM Protocol Role             Master           09:49:18    OK      
 20  L3     VCM Protocol Status (@L5)     Running          21:55:57    OK      
 21  L3     VCM Protocol Status           Running          09:49:18    OK      
 24  L4     VCM Connection                N/A              09:49:18    N/A     
 25  L4     VCM Connection (@L5)          N/A              21:55:57    N/A     
 26  L5     VCM Synchronization           Single-node      09:49:18    NOK_17  
 27  L6     Chassis Sup Connection        N/A              N/A         N/A     
 28  L6     Remote Flash Mounted          N/A              N/A         N/A     
 29  L6     Image and Config Checked      N/A              N/A         N/A     
 30  L6     VC Takeover Sent              Yes              21:55:57    OK      
 31  L7     VC Takeover Acknowledged      Yes              21:55:59    OK      
 32  L8     System Ready Received         Yes              21:55:59    OK      
 33  L8     RCD Operational Status        N/A              N/A         N/A     
 34  L8     RCD IP Address                N/A              N/A         N/A     

Error/Information Codes Detected:
--------------------------------------------------------------------------------
NOK_07
    There are no virtual-fabric links configured on this switch.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port

NOK_07
    There are no virtual-fabric links configured on this switch.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port

NOK_08
    There are no virtual-fabric member ports configured on this switch.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port configured or assigned to each of the 
    virtual-fabric links.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"

NOK_08
    There are no virtual-fabric member ports configured on this switch.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port configured or assigned to each of the 
    virtual-fabric links.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"

NOK_09
    There are no virtual-fabric member interfaces operationally up.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port interface up on each virtual-fabric link.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"
    -> show interfaces port <chassis>/<slot>/<port> status

NOK_09
    There are no virtual-fabric member interfaces operationally up.
    If there are multiple virtual-fabric links configured, we must have
    at least one member port interface up on each virtual-fabric link.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"
    -> show interfaces port <chassis>/<slot>/<port> status

NOK_14
    The virtual-fabric links configured on this switch are not operationally up.
    If there are multiple links configured, all of them must be operationally up
    in order for this parameter to be reported as OK.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link | grep "<local-chassis-id>/"

NOK_14
    The virtual-fabric links configured on this switch are not operationally up.
    If there are multiple links configured, all of them must be operationally up
    in order for this parameter to be reported as OK.
    Troubleshooting Tips:
    -> show virtual-chassis vf-link | grep "<local-chassis-id>/"

NOK_17
    The virtual-chassis manager protocol did not discover any peer switch
    within the discovery time window (i.e. 4 minutes) starting from the time
    we reached the chassis ready state.
    It is possible that there are no other peers, there are no virtual-fabric
    links configured or operational between the switches, or the virtual-chassis
    manager protocol packets are not going through.
    Troubleshooting Tips:
    a) Check if any peer switches were discovered using:
       -> show virtual-chassis topology
    b) If no peers were discovered, check whether the virtual-fabric links are
       operational
       -> show virtual-chassis vf-link member-port | grep "<local-chassis-id>/"



#########################################
debug show virtual-chassis connection
#########################################
                          Address           Address                       
 Chas  MAC-Address        Local IP          Remote IP         Status      
-----+------------------+-----------------+-----------------+-------------



#########################################
show cloud-agent status
#########################################
Admin State                     : Disabled,
Activation Server State         : Unknown,
Device State                    : Initial,
Error State                     : None,
Cloud Group                     : -,
DHCP Address                    : -,
DHCP IP Address Mask            : -,
Gateway                         : -,
Activation Server               : activation.myovcloud.com:443,
Network ID                      : -,
NTP Server                      : ************, *************,
DNS Server                      : ***************,
DNS Domain                      : -,
Proxy Server                    : -,
VPN Server                      : -,
Preprovision Server             : -,
OV Tenant                       : -,
VPN DPD Time (sec)              : 600,
Image Server                    : -,
Image Download Retry Count      : -,
Discovery Interval (min)        : 30,
Time to next Call Home (sec)    : -,
Call Home Timer Status          : Not-Running,
Discovery Retry Count           : 0,
Certificate Status              : -

