@echo off
chcp 65001 >nul
title ALE网络运维工具包 - 一键打包

echo.
echo ========================================
echo    ALE网络运维工具包 - 一键打包工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Python未安装或不在PATH中
    echo 请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✓ Python环境检查通过
echo.

echo 正在安装打包依赖...
python -m pip install pyinstaller>=5.0.0
if %errorlevel% neq 0 (
    echo ✗ 依赖安装失败
    pause
    exit /b 1
)

echo ✓ 依赖安装完成
echo.

echo 开始创建部署包...
python create_deployment.py

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo           打包完成！
    echo ========================================
    echo.
    echo 部署包已创建，包含：
    echo - ALE网络运维工具包.exe （包含所有运行库）
    echo - 配置文件模板
    echo - 使用说明文档
    echo - 启动脚本
    echo.
    echo 可以将整个文件夹复制到任何Windows机器上使用
    echo 无需安装Python环境！
) else (
    echo.
    echo ========================================
    echo           打包失败！
    echo ========================================
    echo 请检查错误信息
)

echo.
pause
