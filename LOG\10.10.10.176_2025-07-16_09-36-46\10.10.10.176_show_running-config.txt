设备: ************
命令: show running-config
执行时间: 2025-07-16 09:36:49
============================================================
Building configuration...

Current configuration : 5753 bytes
!
! Last configuration change at 01:30:36 UTC Wed Jul 16 2025
!
version 17.14
service timestamps debug datetime msec
service timestamps log datetime msec
platform qfp utilization monitor load 80
platform sslvpn use-pd
platform console serial
!
hostname C8K2
!
boot-start-marker
boot-end-marker
!
!
aaa new-model
!
!
aaa authentication login default local
aaa authorization exec default local 
!
!
aaa session-id common
!
!
!
!
!
!
!
!
!
!
!
!
!
!
!
login on-success log
!
!
subscriber templating
!
!
!
crypto pki trustpoint SLA-TrustPoint
 enrollment pkcs12
 revocation-check crl
 hash sha256
!
crypto pki trustpoint TP-self-signed-2302335147
 enrollment selfsigned
 subject-name cn=IOS-Self-Signed-Certificate-2302335147
 revocation-check none
 rsakeypair TP-self-signed-2302335147
 hash sha256
!
!
crypto pki certificate chain SLA-TrustPoint
 certificate ca 01
  30820321 30820209 A0030201 02020101 300D0609 2A864886 F70D0101 0B050030 
  32310E30 0C060355 040A1305 43697363 6F312030 1E060355 04031317 43697363 
  6F204C69 63656E73 696E6720 526F6F74 20434130 1E170D31 33303533 30313934 
  3834375A 170D3338 30353330 31393438 34375A30 32310E30 0C060355 040A1305 
  43697363 6F312030 1E060355 04031317 43697363 6F204C69 63656E73 696E6720 
  526F6F74 20434130 82012230 0D06092A 864886F7 0D010101 05000382 010F0030 
  82010A02 82010100 A6BCBD96 131E05F7 145EA72C 2CD686E6 17222EA1 F1EFF64D 
  CBB4C798 212AA147 C655D8D7 9471380D 8711441E 1AAF071A 9CAE6388 8A38E520 
  1C394D78 462EF239 C659F715 B98C0A59 5BBB5CBD 0CFEBEA3 700A8BF7 D8F256EE 
  4AA4E80D DB6FD1C9 60B1FD18 FFC69C96 6FA68957 A2617DE7 104FDC5F EA2956AC 
  7390A3EB 2B5436AD C847A2C5 DAB553EB 69A9A535 58E9F3E3 C0BD23CF 58BD7188 
  68E69491 20F320E7 948E71D7 AE3BCC84 F10684C7 4BC8E00F 539BA42B 42C68BB7 
  C7479096 B4CB2D62 EA2F505D C7B062A4 6811D95B E8250FC4 5D5D5FB8 8F27D191 
  C55F0D76 61F9A4CD 3D992327 A8BB03BD 4E6D7069 7CBADF8B DF5F4368 95135E44 
  DFC7C6CF 04DD7FD1 02030100 01A34230 40300E06 03551D0F 0101FF04 04030201 
  06300F06 03551D13 0101FF04 05300301 01FF301D 0603551D 0E041604 1449DC85 
  4B3D31E5 1B3E6A17 606AF333 3D3B4C73 E8300D06 092A8648 86F70D01 010B0500 
  03820101 00507F24 D3932A66 86025D9F E838AE5C 6D4DF6B0 49631C78 240DA905 
  604EDCDE FF4FED2B 77FC460E CD636FDB DD44681E 3A5673AB 9093D3B1 6C9E3D8B 
  D98987BF E40CBD9E 1AECA0C2 2189BB5C 8FA85686 CD98B646 5575B146 8DFC66A8 
  467A3DF4 4D565700 6ADF0F0D CF835015 3C04FF7C 21E878AC 11BA9CD2 55A9232C 
  7CA7B7E6 C1AF74F6 152E99B7 B1FCF9BB E973DE7F 5BDDEB86 C71E3B49 1765308B 
  5FB0DA06 B92AFE7F 494E8A9E 07B85737 F3A58BE1 1A48A229 C37C1E69 39F08678 
  80DDCD16 D6BACECA EEBC7CF9 8428787B 35202CDC 60E4616A B623CDBD 230E3AFB 
  418616A9 4093E049 4D10AB75 27E86F73 932E35B5 8862FDAE 0275156F 719BB2F0 
  D697DF7F 28
  	quit
crypto pki certificate chain TP-self-signed-2302335147
 certificate self-signed 01
  30820330 30820218 A0030201 02020101 300D0609 2A864886 F70D0101 0B050030 
  31312F30 2D060355 04030C26 494F532D 53656C66 2D536967 6E65642D 43657274 
  69666963 6174652D 32333032 33333531 3437301E 170D3235 30333033 30323233 
  31395A17 0D333530 33303330 32323331 395A3031 312F302D 06035504 030C2649 
  4F532D53 656C662D 5369676E 65642D43 65727469 66696361 74652D32 33303233 
  33353134 37308201 22300D06 092A8648 86F70D01 01010500 0382010F 00308201 
  0A028201 0100CE96 273F7ED9 FC187A9A 443899DA 4C9E3F55 D5947322 98552B89 
  0DD07520 6D96887C B8E70EA9 4DB57040 0F1314E5 8E9C7E97 91812CD6 0F606BA6 
  A92E49DF B62386B9 8B34EF02 8092EDAE 0E1135A2 CE46BD80 411BD541 6B90C37E 
  B7742530 B6B23CD6 23ADF659 21C7CA74 AA0CE9A6 5BE8893C E9C96E10 3B554F29 
  79E78181 65E68BE8 0D49E90A 788F1377 E7FC9D7D 7AD493F6 9397A29F D416C469 
  33F97350 2D8C1D93 295D62CF 058FFEF9 7F1D6BBE 84002F3E BAAC5AD7 9BFF8E5D 
  6365F3EB A4E81941 36238659 C77B85D2 496EE57F DB53ED87 FD91AE45 35FCADF5 
  EAAB08C2 DC97FC53 D3E3413A 0ACAED06 15B2AFB2 6B494799 1D306BA9 E0C64DA7 
  8FAE0108 0BB70203 010001A3 53305130 1D060355 1D0E0416 0414F9D3 E33DE796 
  880AAE05 F35BEE54 70958768 586E301F 0603551D 23041830 168014F9 D3E33DE7 
  96880AAE 05F35BEE 54709587 68586E30 0F060355 1D130101 FF040530 030101FF 
  300D0609 2A864886 F70D0101 0B050003 82010100 3DA4B775 72374F0B 56A587E6 
  6EDC1852 D304BA27 418CC48F 174ACC70 B856361F 161DE6C7 B9E320D3 4F07D95A 
  871217A4 6DA5D372 C07EDFED 590CB610 A8FEEE3B 63A76F69 53904D29 FA2EAC8D 
  D028F74C 3177EE10 3CAEDD47 68AE3A31 EF44C4F3 EB9B621A 21BC87CD E3A7BA1F 
  45ECF417 4EEA3967 F95CCFC4 B6435E98 9C36C4EB 19084C0C 6B8AF857 DB2C8574 
  266AA5B0 385699E4 841FC86A 2EBF8ADC ******** 99DAFB50 EBD44BB4 2159F3D7 
  9A824962 65120CB1 03543367 1564C6E4 0FE30A4A 5D891C46 FDDC6F4A 76EC9A16 
  834193AA ******** D517DCA2 CB628286 5BE9F290 94A618E4 B9514CD9 9BC71D54 
  772A0EDB 73FD3E9B 76CF891E 60D53261 8DFA4819
  	quit
!
!
license udi pid C8000V sn 9FH174U4YRD
memory free low-watermark processor 225109
diagnostic bootup level minimal
!
!
!
!
username cisco privilege 15 password 0 cisco
username admin privilege 15 password 0 admin
!
redundancy
!
!
!
!
!
!
!
!
!
interface GigabitEthernet1
 ip dhcp client client-id ascii 9FH174U4YRD
 ip address dhcp
 negotiation auto
!
interface GigabitEthernet2
 description My Interface Description
 no ip address
 negotiation auto
!
interface GigabitEthernet3
 description My Interface Description
 no ip address
 negotiation auto
!
interface GigabitEthernet4
 description My Interface Description
 no ip address
 negotiation auto
!
ip forward-protocol nd
!
ip http server
ip http authentication local
ip http secure-server
ip http client source-interface GigabitEthernet1
ip ssh bulk-mode 131072
!
!
!
!
!
!
!
control-plane
!
!
line con 0
 stopbits 1
line aux 0
line vty 0 4
 transport input ssh
line vty 5 7
 transport input ssh
!
!
!
!
!
!
!
restconf
end

============================================================
命令执行完成
