# 网络设备自动化巡检系统使用说明

## 概述

本系统是一个基于Python的网络设备自动化巡检工具，支持批量连接测试、设备信息采集、结果打包和邮件通知等功能。

## 功能特性

- ✅ 批量设备连接测试
- ✅ 多厂商设备支持（Cisco、华为、ALE等）
- ✅ 并发执行，提高效率
- ✅ 自动生成巡检报告
- ✅ 结果自动打包压缩
- ✅ 邮件通知功能
- ✅ 日志记录和管理
- ✅ 交互式和自动化模式

## 环境要求

- Python 3.7+
- 依赖库（见requirements.txt）

## 快速开始

### 方法一：使用启动脚本（推荐）
```bash
python start.py
```
启动脚本会自动检查环境、安装依赖、创建模板，然后启动巡检系统。

### 方法二：使用演示脚本
```bash
python demo.py
```
演示脚本会展示系统的各个功能模块，帮助您了解系统工作流程。

## 安装步骤

1. **克隆或下载项目文件**
2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```
3. **创建配置模板**
   ```bash
   python template_manager.py
   ```

## 文件结构

```
├── network_inspection.py    # 主程序文件
├── connect.py              # 设备连接和信息采集模块
├── send_email.py           # 邮件发送模块
├── zip_file.py             # 文件压缩模块
├── template_manager.py     # Excel模板管理工具
├── start.py                # 启动脚本
├── demo.py                 # 演示脚本
├── requirements.txt        # 依赖库列表
├── template.xlsx           # 设备配置模板
└── LOG/                    # 日志和结果目录
```

## 配置说明

### 1. 设备配置

编辑 `template.xlsx` 文件：

**设备信息工作表：**
- 序号：设备编号
- 状态：填写"#"跳过该设备
- 设备IP：设备管理IP地址
- 协议：ssh 或 telnet
- 端口：SSH端口(22) 或 Telnet端口(23)
- 用户名：登录用户名
- 密码：登录密码
- 特权密码：enable密码（可选）
- 设备类型：cisco_ios, huawei, alcatel_aos 等

**命令配置工作表：**
- 为每种设备类型创建对应的工作表
- 配置要执行的巡检命令
- 状态列填写"#"跳过该命令

### 2. 邮件配置（可选）

编辑 `send_email.py` 文件，配置邮件参数：
```python
sender = '发送者邮箱'
receiver = '接受者邮箱'
# 配置SMTP服务器信息
```

## 使用方法

### 1. 交互式模式

```bash
python network_inspection.py
```

选择菜单选项：
- 1: 设备连接测试
- 2: 设备信息采集
- 3: 完整巡检流程
- 4: 压缩现有结果
- 5: 清理旧日志
- 0: 退出程序

### 2. 命令行模式

**完整巡检流程：**
```bash
python network_inspection.py --auto
```

**仅连接测试：**
```bash
python network_inspection.py --test
```

**仅信息采集：**
```bash
python network_inspection.py --collect
```

## 输出结果

### 日志文件
- `LOG/inspection_YYYYMMDD_HHMMSS.log` - 主程序日志
- `LOG/error_YYYYMMDD_HHMMSS.log` - 错误日志
- `LOG/connect_t_YYYYMMDD_HHMMSS.log` - 连接测试日志

### 设备配置文件
```
LOG/
└── YYYYMMDD_HHMMSS/
    ├── 192.168.1.1_Router01/
    │   ├── show version.conf
    │   ├── show running-config.conf
    │   └── ...
    └── 192.168.1.2_Switch01/
        ├── show version.conf
        └── ...
```

### 汇总报告
- `LOG/inspection_summary_YYYYMMDD_HHMMSS.txt` - 巡检汇总报告

### 压缩文件
- `inspection_results_YYYYMMDD_HHMMSS.zip` - 所有结果的压缩包

## 支持的设备类型

| 厂商 | 设备类型 | 协议支持 |
|------|----------|----------|
| Cisco | cisco_ios | SSH/Telnet |
| 华为 | huawei | SSH/Telnet |
| ALE | alcatel_aos | SSH/Telnet |
| 锐捷 | ruijie_os | SSH/Telnet |
| H3C | hp_comware | SSH/Telnet |

## 常见问题

### 1. 连接超时
- 检查网络连通性
- 确认设备IP地址正确
- 检查防火墙设置

### 2. 认证失败
- 确认用户名密码正确
- 检查设备是否启用SSH/Telnet
- 确认用户权限

### 3. 命令执行失败
- 检查命令语法是否正确
- 确认用户权限是否足够
- 检查设备类型配置是否正确

### 4. 文件权限错误
- 确保程序有写入权限
- 检查磁盘空间是否充足

## 高级配置

### 1. 并发数调整
在 `connect.py` 中修改：
```python
self.pool = ThreadPool(10)  # 调整并发数
```

### 2. 超时时间调整
```python
connect = ConnectHandler(**host, conn_timeout=15)  # 调整超时时间
```

### 3. 日志保留天数
```python
inspection.cleanup_old_logs(keep_days=7)  # 调整保留天数
```

## 技术支持

如遇问题，请检查：
1. Python版本是否符合要求
2. 依赖库是否正确安装
3. 配置文件是否正确
4. 网络连接是否正常
5. 设备权限是否充足

## 更新日志

- v1.0: 初始版本，支持基本巡检功能
- 支持多厂商设备
- 支持并发执行
- 支持结果打包和邮件通知
