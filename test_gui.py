#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
ALE网络运维工具包 - GUI测试版本
简化版本，确保界面和按钮正常显示
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
from datetime import datetime

class SimpleGUI:
    """简化的GUI测试类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("ALE网络运维工具包 v2.0 - 测试版")
        self.root.geometry("800x600")
        self.root.configure(bg="#F5F5F5")
        
        # 居中显示
        self.center_window()
        
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = tk.Frame(self.root, bg="#F5F5F5")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="🔧 ALE网络运维工具包",
            font=("Microsoft YaHei UI", 16, "bold"),
            bg="#F5F5F5",
            fg="#2E86AB"
        )
        title_label.pack(pady=20)
        
        # 内容区域
        content_frame = tk.Frame(main_frame, bg="#F5F5F5")
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        self.create_control_panel(content_frame)
        
        # 右侧日志面板
        self.create_log_panel(content_frame)
        
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_frame = tk.Frame(parent, bg="white", relief=tk.RAISED, bd=1)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.configure(width=300)
        control_frame.pack_propagate(False)
        
        # 面板标题
        tk.Label(
            control_frame,
            text="📋 运维控制面板",
            font=("Microsoft YaHei UI", 12, "bold"),
            bg="white"
        ).pack(pady=15)
        
        # 文件选择
        file_frame = tk.LabelFrame(
            control_frame,
            text="📁 配置文件",
            font=("Microsoft YaHei UI", 10),
            bg="white"
        )
        file_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.excel_path = tk.StringVar(value="template.xlsx")
        excel_entry = tk.Entry(
            file_frame,
            textvariable=self.excel_path,
            font=("Microsoft YaHei UI", 9),
            width=25
        )
        excel_entry.pack(pady=5)
        
        tk.Button(
            file_frame,
            text="浏览文件",
            command=self.browse_file,
            font=("Microsoft YaHei UI", 9)
        ).pack(pady=5)
        
        # 邮件配置
        email_frame = tk.LabelFrame(
            control_frame,
            text="📧 邮件配置",
            font=("Microsoft YaHei UI", 10),
            bg="white"
        )
        email_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.email_status = tk.Label(
            email_frame,
            text="点击检查邮件配置",
            font=("Microsoft YaHei UI", 9),
            bg="white"
        )
        self.email_status.pack(pady=5)
        
        tk.Button(
            email_frame,
            text="检查配置",
            command=self.check_email,
            font=("Microsoft YaHei UI", 9)
        ).pack(pady=5)
        
        # 运维选项
        options_frame = tk.LabelFrame(
            control_frame,
            text="⚙️ 运维选项",
            font=("Microsoft YaHei UI", 10),
            bg="white"
        )
        options_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.auto_email = tk.BooleanVar(value=True)
        self.auto_compress = tk.BooleanVar(value=True)
        
        tk.Checkbutton(
            options_frame,
            text="自动发送邮件",
            variable=self.auto_email,
            font=("Microsoft YaHei UI", 9),
            bg="white"
        ).pack(anchor=tk.W, pady=2)
        
        tk.Checkbutton(
            options_frame,
            text="自动压缩结果",
            variable=self.auto_compress,
            font=("Microsoft YaHei UI", 9),
            bg="white"
        ).pack(anchor=tk.W, pady=2)
        
        # 操作按钮区域
        button_frame = tk.Frame(control_frame, bg="white")
        button_frame.pack(fill=tk.X, padx=15, pady=20)
        
        # 主要按钮 - 开始巡检
        self.start_button = tk.Button(
            button_frame,
            text="🚀 开始巡检",
            command=self.start_inspection,
            font=("Microsoft YaHei UI", 12, "bold"),
            bg="#2E86AB",
            fg="white",
            relief=tk.RAISED,
            bd=2,
            height=2
        )
        self.start_button.pack(fill=tk.X, pady=5)
        
        # 其他按钮
        tk.Button(
            button_frame,
            text="📊 查看结果",
            command=self.view_results,
            font=("Microsoft YaHei UI", 10),
            bg="#A23B72",
            fg="white"
        ).pack(fill=tk.X, pady=2)
        
        tk.Button(
            button_frame,
            text="🧹 清理日志",
            command=self.clean_logs,
            font=("Microsoft YaHei UI", 10),
            bg="#F18F01",
            fg="white"
        ).pack(fill=tk.X, pady=2)
        
    def create_log_panel(self, parent):
        """创建右侧日志面板"""
        log_frame = tk.Frame(parent, bg="white", relief=tk.RAISED, bd=1)
        log_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 日志标题
        tk.Label(
            log_frame,
            text="📝 运维日志",
            font=("Microsoft YaHei UI", 12, "bold"),
            bg="white"
        ).pack(pady=15)
        
        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            font=("Consolas", 9),
            bg="#1E1E1E",
            fg="#FFFFFF",
            insertbackground="#FFFFFF",
            wrap=tk.WORD
        )
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        # 添加欢迎信息
        self.log_message("欢迎使用ALE网络运维工具包!")
        self.log_message("请配置设备文件并检查邮件设置后开始巡检。")
        self.log_message("点击 '🚀 开始巡检' 按钮启动运维流程。")
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        
    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择设备配置文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.excel_path.set(filename)
            self.log_message(f"已选择配置文件: {os.path.basename(filename)}")
            
    def check_email(self):
        """检查邮件配置"""
        if os.path.exists('.env'):
            self.email_status.config(text="✓ 邮件配置文件存在", fg="green")
            self.log_message("邮件配置文件检查通过")
        else:
            self.email_status.config(text="✗ 缺少.env配置文件", fg="red")
            self.log_message("警告: 缺少.env邮件配置文件")
            
    def start_inspection(self):
        """开始巡检"""
        self.log_message("=" * 50)
        self.log_message("🚀 开始ALE网络设备巡检...")
        
        # 检查配置文件
        excel_file = self.excel_path.get()
        if not os.path.exists(excel_file):
            messagebox.showerror("错误", f"配置文件不存在: {excel_file}")
            self.log_message(f"错误: 配置文件不存在 - {excel_file}")
            return
            
        # 检查邮件配置
        if not os.path.exists('.env'):
            messagebox.showwarning("警告", "缺少.env邮件配置文件")
            self.log_message("警告: 缺少邮件配置文件")
        
        # 禁用按钮
        self.start_button.config(state="disabled", text="巡检中...")
        
        # 模拟巡检过程
        self.simulate_inspection()
        
    def simulate_inspection(self):
        """模拟巡检过程"""
        import threading
        import time
        
        def run_simulation():
            try:
                self.log_message("1. 读取设备配置文件...")
                time.sleep(1)
                
                self.log_message("2. 检查网络连接...")
                time.sleep(1)
                
                self.log_message("3. 连接设备...")
                time.sleep(2)
                
                self.log_message("4. 执行巡检命令...")
                time.sleep(2)
                
                self.log_message("5. 收集运维数据...")
                time.sleep(1)
                
                self.log_message("6. 创建压缩包...")
                time.sleep(1)
                
                self.log_message("7. 发送邮件报告...")
                time.sleep(1)
                
                self.log_message("✓ 巡检任务完成!")
                self.log_message("=" * 50)
                
                # 重新启用按钮
                self.root.after(0, lambda: self.start_button.config(state="normal", text="🚀 开始巡检"))
                
            except Exception as e:
                self.log_message(f"巡检过程出错: {e}")
                self.root.after(0, lambda: self.start_button.config(state="normal", text="🚀 开始巡检"))
        
        # 在后台线程运行
        thread = threading.Thread(target=run_simulation)
        thread.daemon = True
        thread.start()
        
    def view_results(self):
        """查看结果"""
        self.log_message("打开结果目录...")
        if os.path.exists("LOG"):
            os.startfile("LOG")
        else:
            messagebox.showinfo("提示", "还没有巡检结果")
            
    def clean_logs(self):
        """清理日志"""
        if messagebox.askyesno("确认", "确定要清理所有日志吗？"):
            self.log_text.delete(1.0, tk.END)
            self.log_message("日志已清理")
            
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    app = SimpleGUI()
    app.run()

if __name__ == '__main__':
    main()
